<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحديث الساعات حسب المنطقة الزمنية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            padding: 20px;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .clock-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        
        .digital-clock {
            font-size: 48px;
            font-weight: bold;
            color: #333;
            text-align: center;
            margin: 10px 0;
        }
        
        .analog-clock {
            width: 200px;
            height: 200px;
            border: 3px solid #333;
            border-radius: 50%;
            position: relative;
            margin: 20px auto;
        }
        
        .hour-hand, .minute-hand, .second-hand {
            position: absolute;
            bottom: 50%;
            left: 50%;
            transform-origin: bottom;
            background: #333;
        }
        
        .hour-hand {
            width: 4px;
            height: 60px;
            margin-left: -2px;
        }
        
        .minute-hand {
            width: 3px;
            height: 80px;
            margin-left: -1.5px;
        }
        
        .second-hand {
            width: 2px;
            height: 90px;
            margin-left: -1px;
            background: #f00;
        }
        
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        
        select, button {
            padding: 10px;
            margin: 5px;
            font-size: 16px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار تحديث الساعات حسب المنطقة الزمنية</h1>
        
        <div class="info">
            <h3>تعليمات الاختبار:</h3>
            <p>1. اختر مدينة من القائمة المنسدلة</p>
            <p>2. ستلاحظ تغيير الساعة الرقمية والتناظرية حسب المنطقة الزمنية للمدينة المختارة</p>
            <p>3. يمكنك أيضاً تغيير طريقة الحساب لاختبار التحديث</p>
        </div>
        
        <div class="controls">
            <label for="city-select">اختر المدينة:</label>
            <select id="city-select">
                <option value="Asia/Amman">عمان (UTC+3)</option>
                <option value="Asia/Riyadh">الرياض (UTC+3)</option>
                <option value="Asia/Dubai">دبي (UTC+4)</option>
                <option value="Asia/Makkah">مكة المكرمة (UTC+3)</option>
                <option value="Asia/Jerusalem">القدس (UTC+2)</option>
                <option value="Africa/Cairo">القاهرة (UTC+2)</option>
                <option value="Asia/Istanbul">إسطنبول (UTC+3)</option>
                <option value="Asia/Tehran">طهران (UTC+3:30)</option>
                <option value="Asia/Karachi">كراتشي (UTC+5)</option>
                <option value="Asia/Kuala_Lumpur">كوالالمبور (UTC+8)</option>
                <option value="Asia/Jakarta">جاكرتا (UTC+7)</option>
            </select>
            
            <label for="method-select">طريقة الحساب:</label>
            <select id="method-select">
                <option value="MWL">رابطة العالم الإسلامي</option>
                <option value="ISNA">الجمعية الإسلامية لأمريكا الشمالية</option>
                <option value="Makkah">جامعة أم القرى</option>
            </select>
            
            <button onclick="updateClocks()">تحديث الساعات</button>
        </div>
        
        <div class="clock-section">
            <h2>الساعة الرقمية</h2>
            <div class="digital-clock" id="digital-clock">00:00:00</div>
        </div>
        
        <div class="clock-section">
            <h2>الساعة التناظرية</h2>
            <div class="analog-clock">
                <div class="hour-hand"></div>
                <div class="minute-hand"></div>
                <div class="second-hand"></div>
            </div>
        </div>
        
        <div class="clock-section">
            <h2>معلومات المنطقة الزمنية</h2>
            <div id="timezone-info">
                <p><strong>المنطقة الزمنية الحالية:</strong> <span id="current-timezone">Asia/Amman</span></p>
                <p><strong>الوقت المحلي:</strong> <span id="local-time">-</span></p>
                <p><strong>الوقت حسب المنطقة المختارة:</strong> <span id="selected-time">-</span></p>
            </div>
        </div>
    </div>

    <script>
        // دالة مساعدة للحصول على الوقت حسب المنطقة الزمنية
        function getCurrentTimeByTimezone(timezone) {
            try {
                const now = new Date();
                return new Date(now.toLocaleString("en-US", {timeZone: timezone}));
            } catch (error) {
                console.error('خطأ في الحصول على الوقت حسب المنطقة الزمنية:', error);
                return new Date();
            }
        }

        // دالة لتحديث الساعة التناظرية
        function updateAnalogClock(timezone) {
            const now = getCurrentTimeByTimezone(timezone);
            const hours = now.getHours() % 12;
            const minutes = now.getMinutes();
            const seconds = now.getSeconds();
            
            const hourHand = document.querySelector('.hour-hand');
            const minuteHand = document.querySelector('.minute-hand');
            const secondHand = document.querySelector('.second-hand');
            
            if (hourHand && minuteHand && secondHand) {
                const hourDeg = (hours * 30) + (minutes * 0.5);
                const minuteDeg = (minutes * 6) + (seconds * 0.1);
                const secondDeg = seconds * 6;
                
                hourHand.style.transform = `rotate(${hourDeg}deg)`;
                minuteHand.style.transform = `rotate(${minuteDeg}deg)`;
                secondHand.style.transform = `rotate(${secondDeg}deg)`;
            }
        }

        // دالة لتحديث الساعة الرقمية
        function updateDigitalClock(timezone) {
            const now = getCurrentTimeByTimezone(timezone);
            const hours = now.getHours();
            const minutes = now.getMinutes();
            const seconds = now.getSeconds();
            
            const timeString = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
            
            const digitalClock = document.getElementById('digital-clock');
            if (digitalClock) {
                digitalClock.textContent = timeString;
            }
        }

        // دالة لتحديث معلومات المنطقة الزمنية
        function updateTimezoneInfo(timezone) {
            const localNow = new Date();
            const selectedNow = getCurrentTimeByTimezone(timezone);
            
            document.getElementById('current-timezone').textContent = timezone;
            document.getElementById('local-time').textContent = localNow.toLocaleString('ar-SA');
            document.getElementById('selected-time').textContent = selectedNow.toLocaleString('ar-SA');
        }

        // دالة لتحديث جميع الساعات
        function updateAllClocks() {
            const selectedCity = document.getElementById('city-select').value;
            
            updateAnalogClock(selectedCity);
            updateDigitalClock(selectedCity);
            updateTimezoneInfo(selectedCity);
        }

        // دالة تحديث الساعات (لزر التحديث)
        function updateClocks() {
            updateAllClocks();
            alert('تم تحديث الساعات حسب المنطقة الزمنية المختارة!');
        }

        // إعداد مستمعات الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            // مستمع لتغيير المدينة
            document.getElementById('city-select').addEventListener('change', function() {
                console.log('تم تغيير المدينة إلى:', this.value);
                updateAllClocks();
            });
            
            // مستمع لتغيير طريقة الحساب
            document.getElementById('method-select').addEventListener('change', function() {
                console.log('تم تغيير طريقة الحساب إلى:', this.value);
                // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
            });
            
            // تحديث أولي
            updateAllClocks();
            
            // تحديث مستمر كل ثانية
            setInterval(updateAllClocks, 1000);
        });
    </script>
</body>
</html> 