<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المكتبات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>اختبار المكتبات الأساسية</h1>
    
    <div class="test-section">
        <h3>1. اختبار تحميل المكتبات:</h3>
        <div id="library-test"></div>
    </div>
    
    <div class="test-section">
        <h3>2. اختبار مكتبة moment:</h3>
        <div id="moment-test"></div>
    </div>
    
    <div class="test-section">
        <h3>3. اختبار مكتبة PrayTimes:</h3>
        <div id="praytimes-test"></div>
    </div>
    
    <div class="test-section">
        <h3>4. اختبار حساب مواقيت الصلاة:</h3>
        <div id="prayer-test"></div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="node_modules/moment/min/moment.min.js"></script>
    <script src="node_modules/moment-hijri/moment-hijri.js"></script>
    <script src="js/PrayTimes.js"></script>

    <script>
        // اختبار المكتبات
        function testLibraries() {
            const testDiv = document.getElementById('library-test');
            let html = '';
            
            // فحص المكتبات
            const libraries = [
                { 
                    name: 'moment', 
                    check: () => typeof moment !== 'undefined',
                    details: () => `الإصدار: ${moment.version}`
                },
                { 
                    name: 'moment-hijri', 
                    check: () => typeof moment.hijri !== 'undefined',
                    details: () => 'متاح'
                },
                { 
                    name: 'PrayTimes', 
                    check: () => typeof PrayTimes !== 'undefined',
                    details: () => 'متاح'
                }
            ];
            
            libraries.forEach(lib => {
                if (lib.check()) {
                    html += `<div class="success">✅ ${lib.name} - ${lib.details()}</div>`;
                } else {
                    html += `<div class="error">❌ ${lib.name} - غير متاح</div>`;
                }
            });
            
            testDiv.innerHTML = html;
        }
        
        // اختبار moment
        function testMoment() {
            const testDiv = document.getElementById('moment-test');
            
            try {
                const now = moment();
                const hijriDate = moment().format('iYYYY/iM/iD');
                const gregorianDate = now.format('YYYY-MM-DD');
                
                testDiv.innerHTML = `
                    <div class="success">
                        <strong>التاريخ الميلادي:</strong> ${gregorianDate}<br>
                        <strong>التاريخ الهجري:</strong> ${hijriDate}<br>
                        <strong>الوقت الحالي:</strong> ${now.format('HH:mm:ss')}
                    </div>
                `;
            } catch (error) {
                testDiv.innerHTML = `<div class="error">❌ خطأ في moment: ${error.message}</div>`;
            }
        }
        
        // اختبار PrayTimes
        function testPrayTimes() {
            const testDiv = document.getElementById('praytimes-test');
            
            try {
                if (typeof PrayTimes === 'undefined') {
                    testDiv.innerHTML = `<div class="error">❌ مكتبة PrayTimes غير متوفرة</div>`;
                    return;
                }
                
                const pt = new PrayTimes('MWL');
                const methods = pt.getMethods();
                
                let methodsHtml = '<strong>طرق الحساب المتاحة:</strong><br>';
                for (let method in methods) {
                    methodsHtml += `- ${method}: ${methods[method].name}<br>`;
                }
                
                testDiv.innerHTML = `
                    <div class="success">
                        ✅ مكتبة PrayTimes تعمل بشكل صحيح<br>
                        ${methodsHtml}
                    </div>
                `;
            } catch (error) {
                testDiv.innerHTML = `<div class="error">❌ خطأ في PrayTimes: ${error.message}</div>`;
            }
        }
        
        // اختبار حساب مواقيت الصلاة
        function testPrayerCalculation() {
            const testDiv = document.getElementById('prayer-test');
            
            try {
                if (typeof PrayTimes === 'undefined') {
                    testDiv.innerHTML = `<div class="error">❌ مكتبة PrayTimes غير متوفرة</div>`;
                    return;
                }
                
                const pt = new PrayTimes('MWL');
                const coordinates = [31.9539, 35.9106]; // عمان
                const date = new Date();
                const times = pt.getTimes(date, coordinates, 3); // UTC+3
                
                let timesHtml = '<strong>مواقيت الصلاة لعمان:</strong><br>';
                for (let prayer in times) {
                    if (times[prayer] !== '-----') {
                        timesHtml += `- ${prayer}: ${times[prayer]}<br>`;
                    }
                }
                
                testDiv.innerHTML = `
                    <div class="success">
                        ✅ حساب مواقيت الصلاة يعمل<br>
                        ${timesHtml}
                    </div>
                `;
            } catch (error) {
                testDiv.innerHTML = `<div class="error">❌ خطأ في حساب مواقيت الصلاة: ${error.message}</div>`;
            }
        }
        
        // تشغيل الاختبارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('بدء اختبار المكتبات...');
            testLibraries();
            testMoment();
            testPrayTimes();
            testPrayerCalculation();
        });
    </script>
</body>
</html> 