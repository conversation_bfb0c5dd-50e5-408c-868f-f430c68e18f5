<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التطبيق الرئيسي - تحديث الساعات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: white;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .clock-display {
            text-align: center;
            margin: 20px 0;
        }
        
        .analog-clock {
            width: 150px;
            height: 150px;
            border: 3px solid #fff;
            border-radius: 50%;
            margin: 20px auto;
            position: relative;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .hour-hand, .minute-hand, .second-hand {
            position: absolute;
            top: 50%;
            left: 50%;
            transform-origin: 50% 0;
            background: #fff;
        }
        
        .hour-hand {
            width: 3px;
            height: 45px;
            margin-left: -1.5px;
            margin-top: -45px;
        }
        
        .minute-hand {
            width: 2px;
            height: 60px;
            margin-left: -1px;
            margin-top: -60px;
        }
        
        .second-hand {
            width: 1px;
            height: 70px;
            margin-left: -0.5px;
            margin-top: -70px;
            background: #ff6b6b;
        }
        
        .digital-clock {
            font-size: 1.5em;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .control-group {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
        }
        
        select, button, input {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: none;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 14px;
        }
        
        select option {
            background: #333;
            color: white;
        }
        
        button {
            background: #4CAF50;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 14px;
        }
        
        .log {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .info { color: #2196F3; }
        .warning { color: #ff9800; }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار التطبيق الرئيسي - تحديث الساعات</h1>
        
        <div class="test-section">
            <h2>الساعات الحالية</h2>
            <div class="clock-display">
                <div class="analog-clock">
                    <div class="hour-hand"></div>
                    <div class="minute-hand"></div>
                    <div class="second-hand"></div>
                </div>
                <div class="digital-clock" id="digital-clock">00:00:00</div>
                <div class="status" id="status">حالة الساعة: جاري التحميل...</div>
            </div>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <h3>اختيار المدينة</h3>
                <select id="city-select">
                    <option value="Asia/Amman">عمان - الأردن</option>
                    <option value="Asia/Riyadh">الرياض - السعودية</option>
                    <option value="Asia/Dubai">دبي - الإمارات</option>
                    <option value="Asia/Cairo">القاهرة - مصر</option>
                    <option value="Asia/Beirut">بيروت - لبنان</option>
                    <option value="Asia/Kuwait">الكويت - الكويت</option>
                    <option value="Asia/Qatar">الدوحة - قطر</option>
                </select>
            </div>
            
            <div class="control-group">
                <h3>طريقة الحساب</h3>
                <select id="method-select">
                    <option value="MWL">MWL - Muslim World League</option>
                    <option value="ISNA">ISNA - Islamic Society of North America</option>
                    <option value="Egypt">Egypt - Egyptian General Authority</option>
                    <option value="Makkah">Makkah - Umm Al-Qura University</option>
                    <option value="Karachi">Karachi - University of Islamic Sciences</option>
                    <option value="Tehran">Tehran - Institute of Geophysics</option>
                    <option value="Jafari">Jafari - Shia Ithna Ashari</option>
                </select>
            </div>
            
            <div class="control-group">
                <h3>المذهب</h3>
                <select id="madhab-select">
                    <option value="Shafi">شافعي</option>
                    <option value="Hanafi">حنفي</option>
                </select>
            </div>
        </div>
        
        <div class="test-section">
            <h3>اختبار التحديث</h3>
            <div class="controls">
                <button onclick="testCityChange()">اختبار تغيير المدينة</button>
                <button onclick="testMethodChange()">اختبار تغيير الدالة</button>
                <button onclick="testMadhabChange()">اختبار تغيير المذهب</button>
                <button onclick="testAllChanges()">اختبار جميع التغييرات</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>سجل الأحداث</h3>
            <div class="log" id="event-log"></div>
            <button onclick="clearLog()">مسح السجل</button>
        </div>
        
        <div class="test-section">
            <h3>معلومات النظام</h3>
            <div class="status" id="system-info">جاري تحميل معلومات النظام...</div>
        </div>
    </div>

    <script>
        // متغيرات النظام
        let currentCity = 'Asia/Amman';
        let currentMethod = 'MWL';
        let currentMadhab = 'Shafi';
        let updateCount = 0;
        
        // دوال الساعة
        function updateAnalogClock() {
            const now = new Date();
            const hours = now.getHours() % 12;
            const minutes = now.getMinutes();
            const seconds = now.getSeconds();
            
            const hourHand = document.querySelector('.hour-hand');
            const minuteHand = document.querySelector('.minute-hand');
            const secondHand = document.querySelector('.second-hand');
            
            if (hourHand && minuteHand && secondHand) {
                const hourDeg = (hours * 30) + (minutes * 0.5);
                const minuteDeg = (minutes * 6) + (seconds * 0.1);
                const secondDeg = seconds * 6;
                
                hourHand.style.transform = `rotate(${hourDeg}deg)`;
                minuteHand.style.transform = `rotate(${minuteDeg}deg)`;
                secondHand.style.transform = `rotate(${secondDeg}deg)`;
            }
        }

        function updateDigitalClock() {
            const now = new Date();
            const hours = now.getHours();
            const minutes = now.getMinutes();
            const seconds = now.getSeconds();
            
            const timeString = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
            
            const digitalClock = document.getElementById('digital-clock');
            if (digitalClock) {
                digitalClock.textContent = timeString;
            }
        }

        function updateStatus(message) {
            const status = document.getElementById('status');
            if (status) {
                status.textContent = `حالة الساعة: ${message}`;
            }
        }

        function logEvent(message, type = 'info') {
            const log = document.getElementById('event-log');
            if (log) {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = type;
                logEntry.textContent = `[${timestamp}] ${message}`;
                log.appendChild(logEntry);
                log.scrollTop = log.scrollHeight;
            }
        }

        function clearLog() {
            const log = document.getElementById('event-log');
            if (log) {
                log.innerHTML = '';
            }
        }

        function updateSystemInfo() {
            const info = document.getElementById('system-info');
            if (info) {
                info.innerHTML = `
                    المدينة الحالية: ${currentCity}<br>
                    طريقة الحساب: ${currentMethod}<br>
                    المذهب: ${currentMadhab}<br>
                    عدد التحديثات: ${updateCount}<br>
                    الوقت الحالي: ${new Date().toLocaleString('ar-SA')}
                `;
            }
        }

        function updateAllClocks() {
            updateCount++;
            logEvent(`تحديث الساعات #${updateCount}`, 'success');
            
            updateAnalogClock();
            updateDigitalClock();
            updateStatus('تم التحديث بنجاح');
            updateSystemInfo();
        }

        // دوال الاختبار
        function testCityChange() {
            const citySelect = document.getElementById('city-select');
            const newCity = citySelect.value;
            
            if (newCity !== currentCity) {
                logEvent(`تغيير المدينة من ${currentCity} إلى ${newCity}`, 'info');
                currentCity = newCity;
                
                // محاكاة تغيير المدينة
                setTimeout(() => {
                    updateAllClocks();
                    logEvent(`تم تحديث الساعات بعد تغيير المدينة`, 'success');
                }, 100);
            } else {
                logEvent('المدينة لم تتغير', 'warning');
            }
        }

        function testMethodChange() {
            const methodSelect = document.getElementById('method-select');
            const newMethod = methodSelect.value;
            
            if (newMethod !== currentMethod) {
                logEvent(`تغيير طريقة الحساب من ${currentMethod} إلى ${newMethod}`, 'info');
                currentMethod = newMethod;
                
                // محاكاة تغيير طريقة الحساب
                setTimeout(() => {
                    updateAllClocks();
                    logEvent(`تم تحديث الساعات بعد تغيير طريقة الحساب`, 'success');
                }, 100);
            } else {
                logEvent('طريقة الحساب لم تتغير', 'warning');
            }
        }

        function testMadhabChange() {
            const madhabSelect = document.getElementById('madhab-select');
            const newMadhab = madhabSelect.value;
            
            if (newMadhab !== currentMadhab) {
                logEvent(`تغيير المذهب من ${currentMadhab} إلى ${newMadhab}`, 'info');
                currentMadhab = newMadhab;
                
                // محاكاة تغيير المذهب
                setTimeout(() => {
                    updateAllClocks();
                    logEvent(`تم تحديث الساعات بعد تغيير المذهب`, 'success');
                }, 100);
            } else {
                logEvent('المذهب لم يتغير', 'warning');
            }
        }

        function testAllChanges() {
            logEvent('بدء اختبار جميع التغييرات', 'info');
            
            // تغيير المدينة
            setTimeout(() => {
                testCityChange();
                
                // تغيير طريقة الحساب
                setTimeout(() => {
                    testMethodChange();
                    
                    // تغيير المذهب
                    setTimeout(() => {
                        testMadhabChange();
                        logEvent('انتهى اختبار جميع التغييرات', 'success');
                    }, 500);
                }, 500);
            }, 500);
        }

        // مراقبة التغييرات
        function setupChangeMonitoring() {
            const citySelect = document.getElementById('city-select');
            const methodSelect = document.getElementById('method-select');
            const madhabSelect = document.getElementById('madhab-select');
            
            if (citySelect) {
                citySelect.addEventListener('change', function() {
                    logEvent(`تم تغيير المدينة إلى: ${this.value}`, 'info');
                    currentCity = this.value;
                    setTimeout(updateAllClocks, 100);
                });
            }
            
            if (methodSelect) {
                methodSelect.addEventListener('change', function() {
                    logEvent(`تم تغيير طريقة الحساب إلى: ${this.value}`, 'info');
                    currentMethod = this.value;
                    setTimeout(updateAllClocks, 100);
                });
            }
            
            if (madhabSelect) {
                madhabSelect.addEventListener('change', function() {
                    logEvent(`تم تغيير المذهب إلى: ${this.value}`, 'info');
                    currentMadhab = this.value;
                    setTimeout(updateAllClocks, 100);
                });
            }
        }

        // بدء تشغيل النظام
        function startSystem() {
            logEvent('بدء تشغيل نظام اختبار الساعات', 'info');
            
            // تحديث أولي
            updateAllClocks();
            
            // بدء التحديث المستمر
            setInterval(updateAnalogClock, 1000);
            setInterval(updateDigitalClock, 1000);
            setInterval(updateSystemInfo, 5000);
            
            // إعداد مراقبة التغييرات
            setupChangeMonitoring();
            
            logEvent('تم بدء تشغيل النظام بنجاح', 'success');
        }

        // بدء التشغيل عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            logEvent('تحميل صفحة اختبار الساعات', 'info');
            setTimeout(startSystem, 500);
        });
    </script>
</body>
</html> 