# تحديث الساعات حسب المنطقة الزمنية

## نظرة عامة
تم تحديث نظام الساعة ليدعم تغيير المنطقة الزمنية تلقائياً عند تغيير المدينة أو الدالة (طريقة الحساب). الآن ستتغير الساعة الرقمية والساعة التناظرية والتاريخ والعد التنازلي حسب المنطقة الزمنية للمدينة المختارة.

## التحديثات المضافة

### 1. دالة `getCurrentTimeByTimezone()`
- دالة مساعدة للحصول على الوقت حسب المنطقة الزمنية المختارة
- تستخدم `localStorage.getItem('selectedCity')` للحصول على المدينة المختارة
- تعيد الوقت المحلي كاحتياطي في حالة حدوث خطأ

### 2. تحديث دوال الساعة
#### `updateAnalogClock()`
- تم تحديثها لاستخدام المنطقة الزمنية المختارة
- تستخدم `getCurrentTimeByTimezone()` بدلاً من `new Date()`

#### `updateDigitalClock()`
- تم تحديثها لاستخدام المنطقة الزمنية المختارة
- تدعم نظام 12 و 24 ساعة حسب الإعدادات

#### `updateDate()`
- تم تحديثها لاستخدام المنطقة الزمنية المختارة
- تعرض التاريخ الميلادي والهجري حسب المنطقة الزمنية

#### `updateCountdown()`
- تم تحديثها لاستخدام المنطقة الزمنية المختارة
- تحسب العد التنازلي للصلاة القادمة حسب الوقت المحلي للمدينة

### 3. دالة `updateAllClocks()`
- دالة شاملة لتحديث جميع الساعات دفعة واحدة
- تستدعي جميع دوال التحديث بالترتيب الصحيح
- مفيدة عند تغيير المدينة أو المنطقة الزمنية

### 4. مستمعات الأحداث
#### `setupClockEventListeners()`
- مستمع لحدث `cityChanged`
- مستمع لحدث `timezoneChanged`
- مستمع لحدث `calculationMethodChanged`
- جميع المستمعات تستدعي `updateAllClocks()` مع تأخير صغير

### 5. التكامل مع النظام الحالي
#### تحديث `timezone-sync.js`
- تم تحديث دالة `syncClocks()` لاستخدام `updateAllClocks()`
- إضافة احتياطي في حالة عدم توفر الدالة الجديدة

#### تحديث `index.html`
- إضافة مستمعات الأحداث لتحديث الساعات عند تغيير المدينة
- تحديث في `city-select` و `selected-city`
- تحديث في `setupCityAndMethodListeners()`

## كيفية الاستخدام

### 1. تغيير المدينة
```javascript
// عند تغيير المدينة، ستتحدث الساعات تلقائياً
localStorage.setItem('selectedCity', 'Asia/Dubai');
// أو
document.getElementById('city-select').value = 'Asia/Dubai';
```

### 2. تحديث الساعات يدوياً
```javascript
// تحديث جميع الساعات
if (typeof updateAllClocks === 'function') {
    updateAllClocks();
}

// تحديث ساعة محددة
if (typeof updateAnalogClock === 'function') {
    updateAnalogClock();
}
```

### 3. الحصول على الوقت حسب المنطقة الزمنية
```javascript
// الحصول على الوقت الحالي حسب المنطقة الزمنية المختارة
const currentTime = getCurrentTimeByTimezone();
```

## المدن المدعومة

### المناطق الزمنية المدعومة:
- `Asia/Amman` (UTC+3) - عمان
- `Asia/Riyadh` (UTC+3) - الرياض
- `Asia/Dubai` (UTC+4) - دبي
- `Asia/Makkah` (UTC+3) - مكة المكرمة
- `Asia/Madinah` (UTC+3) - المدينة المنورة
- `Asia/Jerusalem` (UTC+2) - القدس
- `Africa/Cairo` (UTC+2) - القاهرة
- `Asia/Istanbul` (UTC+3) - إسطنبول
- `Asia/Tehran` (UTC+3:30) - طهران
- `Asia/Karachi` (UTC+5) - كراتشي
- `Asia/Kuala_Lumpur` (UTC+8) - كوالالمبور
- `Asia/Jakarta` (UTC+7) - جاكرتا

## الاختبار

### ملف الاختبار: `test-timezone-clocks.html`
- اختبار تفاعلي لتغيير المدينة ومراقبة تحديث الساعات
- عرض معلومات المنطقة الزمنية
- مقارنة الوقت المحلي مع الوقت حسب المنطقة المختارة

### كيفية الاختبار:
1. افتح ملف `test-timezone-clocks.html`
2. اختر مدينة من القائمة المنسدلة
3. لاحظ تغيير الساعة الرقمية والتناظرية
4. تحقق من معلومات المنطقة الزمنية

## الأحداث المطلقة

### `cityChanged`
```javascript
window.dispatchEvent(new CustomEvent('cityChanged', {
    detail: { 
        city: 'Asia/Dubai', 
        timezone: 'Asia/Dubai' 
    }
}));
```

### `timezoneChanged`
```javascript
window.dispatchEvent(new CustomEvent('timezoneChanged', {
    detail: { 
        timezone: 'Asia/Dubai',
        cityKey: 'Asia/Dubai'
    }
}));
```

### `calculationMethodChanged`
```javascript
window.dispatchEvent(new CustomEvent('calculationMethodChanged', {
    detail: { 
        method: 'MWL',
        city: 'Asia/Dubai'
    }
}));
```

## ملاحظات مهمة

### 1. التوافق مع المتصفحات
- يستخدم `toLocaleString()` مع `timeZone` parameter
- يعمل مع جميع المتصفحات الحديثة
- يحتوي على احتياطي للوقت المحلي في حالة الخطأ

### 2. الأداء
- تحديث كل ثانية للساعات
- استخدام `setTimeout` لتأخير صغير عند تغيير المدينة
- تحسين الأداء عبر دالة مساعدة واحدة

### 3. التخزين
- يستخدم `localStorage` لحفظ المدينة المختارة
- يحفظ الإعدادات تلقائياً
- يسترجع الإعدادات عند إعادة تحميل الصفحة

## استكشاف الأخطاء

### مشكلة: الساعات لا تتحدث عند تغيير المدينة
**الحل:**
1. تأكد من وجود عنصر `city-select` أو `selected-city`
2. تحقق من وجود دالة `updateAllClocks`
3. راجع وحدة التحكم للأخطاء

### مشكلة: الوقت غير صحيح
**الحل:**
1. تحقق من صحة اسم المنطقة الزمنية
2. تأكد من دعم المتصفح للمنطقة الزمنية
3. راجع احتياطي الوقت المحلي

### مشكلة: الساعات لا تعمل
**الحل:**
1. تأكد من تحميل ملف `clock-new.js`
2. تحقق من وجود عناصر DOM المطلوبة
3. راجع ترتيب تحميل الملفات

## التطوير المستقبلي

### إمكانيات للتطوير:
1. دعم المزيد من المناطق الزمنية
2. إضافة خيار التوقيت الصيفي
3. تحسين الأداء عبر Web Workers
4. إضافة خيارات تخصيص إضافية للساعات

### تحسينات مقترحة:
1. إضافة مؤشر للمنطقة الزمنية في واجهة المستخدم
2. دعم تخصيص تنسيق الوقت
3. إضافة خيارات للتاريخ الهجري
4. تحسين تجربة المستخدم عند تغيير المدينة 