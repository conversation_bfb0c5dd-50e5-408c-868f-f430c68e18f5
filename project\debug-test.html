<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تشخيص المشكلة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .clock-display {
            font-size: 3em;
            margin: 20px 0;
            padding: 20px;
            background: #333;
            color: #40E0D0;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            text-align: center;
        }
        
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        
        .controls button {
            background: #40E0D0;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        
        .controls button:hover {
            background: #369fb8;
        }
        
        .info {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: right;
        }
        
        .log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار تشخيص المشكلة</h1>
        
        <div class="info">
            <strong>الهدف:</strong> تشخيص سبب عدم تغيير الوقت ومواقيت الصلاة
        </div>
        
        <div class="clock-display" id="test-clock">
            --:--:--
        </div>
        
        <div class="info">
            <div><strong>المدينة الحالية:</strong> <span id="current-city">--</span></div>
            <div><strong>المنطقة الزمنية:</strong> <span id="current-timezone">--</span></div>
        </div>
        
        <div class="controls">
            <button onclick="testAmman()">اختبار عمان</button>
            <button onclick="testDubai()">اختبار دبي</button>
            <button onclick="testCairo()">اختبار القاهرة</button>
            <button onclick="checkFunctions()">فحص الدوال</button>
            <button onclick="clearLog()">مسح السجل</button>
        </div>
        
        <div class="log" id="debug-log">
            جاري التحميل...
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="cities-database.js"></script>
    <script src="clock-new.js"></script>
    <script src="prayer-times-new.js"></script>
    
    <script>
        let logElement;
        
        function log(message) {
            if (!logElement) logElement = document.getElementById('debug-log');
            const time = new Date().toLocaleTimeString();
            logElement.innerHTML += `<div>[${time}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            if (!logElement) logElement = document.getElementById('debug-log');
            logElement.innerHTML = '';
        }
        
        function updateDisplay() {
            try {
                const selectedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                document.getElementById('current-timezone').textContent = selectedCity;
                
                if (typeof getCurrentTimeForSelectedCity === 'function') {
                    const cityTime = getCurrentTimeForSelectedCity();
                    const timeString = cityTime.toLocaleTimeString('ar-SA');
                    document.getElementById('test-clock').textContent = timeString;
                    
                    // تحديث معلومات المدينة
                    if (typeof CITIES_DATABASE !== 'undefined' && CITIES_DATABASE[selectedCity]) {
                        document.getElementById('current-city').textContent = CITIES_DATABASE[selectedCity].name;
                    } else {
                        document.getElementById('current-city').textContent = selectedCity;
                    }
                    
                    log(`✅ تم تحديث الساعة: ${timeString} للمدينة: ${selectedCity}`);
                } else {
                    log('❌ دالة getCurrentTimeForSelectedCity غير متوفرة');
                    document.getElementById('test-clock').textContent = 'خطأ في الدالة';
                }
            } catch (error) {
                log(`❌ خطأ في تحديث العرض: ${error.message}`);
            }
        }
        
        function testAmman() {
            log('🧪 اختبار عمان...');
            localStorage.setItem('selectedCity', 'Asia/Amman');
            updateDisplay();
        }
        
        function testDubai() {
            log('🧪 اختبار دبي...');
            localStorage.setItem('selectedCity', 'Asia/Dubai');
            updateDisplay();
        }
        
        function testCairo() {
            log('🧪 اختبار القاهرة...');
            localStorage.setItem('selectedCity', 'Africa/Cairo');
            updateDisplay();
        }
        
        function checkFunctions() {
            log('🔍 فحص الدوال المطلوبة...');
            
            const functions = [
                'getCurrentTimeForSelectedCity',
                'updateAnalogClock',
                'updateDigitalClock',
                'getPrayerTimes',
                'reloadPrayerTimes',
                'updateCountdown'
            ];
            
            functions.forEach(funcName => {
                const exists = typeof window[funcName] === 'function';
                log(`${exists ? '✅' : '❌'} ${funcName}: ${typeof window[funcName]}`);
            });
            
            // فحص المتغيرات
            const variables = [
                'CITIES_DATABASE',
                'PRAYER_TIMES_DATA',
                'prayerTimes'
            ];
            
            variables.forEach(varName => {
                const exists = typeof window[varName] !== 'undefined';
                log(`${exists ? '✅' : '❌'} ${varName}: ${typeof window[varName]}`);
            });
        }
        
        // بدء الاختبار
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء اختبار التشخيص...');
            
            setTimeout(() => {
                checkFunctions();
                updateDisplay();
                
                // تحديث كل ثانية
                setInterval(updateDisplay, 1000);
                
                log('✅ تم بدء الاختبار بنجاح');
            }, 1000);
        });
        
        // اختبار تغيير المدينة كل 5 ثوان
        setTimeout(() => {
            log('🔄 اختبار تلقائي - تغيير إلى دبي...');
            testDubai();
            
            setTimeout(() => {
                log('🔄 اختبار تلقائي - العودة إلى عمان...');
                testAmman();
            }, 3000);
        }, 5000);
    </script>
</body>
</html>
