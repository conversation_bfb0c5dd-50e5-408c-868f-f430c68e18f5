/**
 * ملف الساعة والتعتيم الجديد
 * يحتوي على جميع الدوال المتعلقة بالساعة والتعتيم
 */

// متغيرات عالمية
let iqamahTimer = null;
let darknessTimer = null;
let digitalClockInterval = null;

// تهيئة مدة الإقامة
window.iqamahTimes = {
    fajr: 10,
    dhuhr: 10,
    asr: 10,
    maghrib: 10,
    isha: 10
};

// تهيئة مدة التعتيم
window.darknessTimes = {
    fajr: 10,
    dhuhr: 10,
    asr: 10,
    maghrib: 10,
    isha: 10
};

// دالة للحصول على الوقت الدقيق حسب المنطقة الزمنية للمدينة المختارة
function getCurrentTimeForSelectedCity() {
    try {
        const selectedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
        let timezone = selectedCity;

        console.log('🕐 حساب الوقت للمدينة:', selectedCity);

        // إذا كان selectedCity يحتوي على منطقة زمنية مباشرة
        if (selectedCity.includes('/')) {
            timezone = selectedCity;
        } else {
            // البحث عن المنطقة الزمنية في قاعدة البيانات
            if (typeof CITIES_DATABASE !== 'undefined' && CITIES_DATABASE[selectedCity]) {
                const cityData = CITIES_DATABASE[selectedCity];
                if (cityData.timezone) {
                    if (typeof cityData.timezone === 'string') {
                        timezone = cityData.timezone;
                    } else {
                        // تحويل offset إلى منطقة زمنية
                        const timezoneMap = {
                            3: 'Asia/Amman',      // الأردن، فلسطين، لبنان
                            4: 'Asia/Dubai',      // الإمارات، عُمان
                            2: 'Africa/Cairo',    // مصر
                            1: 'Africa/Tunis',    // تونس، الجزائر
                            0: 'Europe/London',   // المغرب (GMT)
                            5: 'Asia/Karachi',    // باكستان
                            6: 'Asia/Dhaka'       // بنغلاديش
                        };
                        timezone = timezoneMap[cityData.timezone] || 'Asia/Amman';
                    }
                }
            } else {
                // خريطة شاملة للمدن والمناطق الزمنية
                const cityTimezoneMap = {
                    // الأردن
                    'عمان': 'Asia/Amman', 'Zarqa': 'Asia/Amman', 'Irbid': 'Asia/Amman', 'Aqaba': 'Asia/Amman',
                    // السعودية
                    'الرياض': 'Asia/Riyadh', 'Jeddah': 'Asia/Riyadh', 'مكة': 'Asia/Riyadh', 'المدينة': 'Asia/Riyadh',
                    // الإمارات
                    'دبي': 'Asia/Dubai', 'Abu_Dhabi': 'Asia/Dubai',
                    // مصر
                    'القاهرة': 'Africa/Cairo', 'Alexandria': 'Africa/Cairo',
                    // دول أخرى
                    'بغداد': 'Asia/Baghdad', 'الكويت': 'Asia/Kuwait', 'الدوحة': 'Asia/Qatar',
                    'المنامة': 'Asia/Bahrain', 'مسقط': 'Asia/Muscat', 'بيروت': 'Asia/Beirut',
                    'دمشق': 'Asia/Damascus', 'القدس': 'Asia/Jerusalem', 'إسطنبول': 'Europe/Istanbul'
                };
                timezone = cityTimezoneMap[selectedCity] || 'Asia/Amman';
            }
        }

        console.log('🌍 المنطقة الزمنية المستخدمة:', timezone);

        // التحقق من صحة المنطقة الزمنية
        try {
            // اختبار المنطقة الزمنية
            const testDate = new Date();
            testDate.toLocaleString("en-US", {timeZone: timezone});
        } catch (timezoneError) {
            console.warn('⚠️ منطقة زمنية غير صحيحة، استخدام Asia/Amman');
            timezone = 'Asia/Amman';
        }

        // الحصول على الوقت الدقيق حسب المنطقة الزمنية
        const now = new Date();
        const cityTime = new Date(now.toLocaleString("en-US", {timeZone: timezone}));

        // تصحيح أي اختلاف في الميلي ثانية
        const correctedTime = new Date(cityTime.getTime());

        console.log('🕐 الوقت المحلي:', now.toLocaleTimeString('ar-SA'));
        console.log('🌍 وقت المدينة:', correctedTime.toLocaleTimeString('ar-SA'));
        console.log('⏰ فرق التوقيت:', Math.round((correctedTime.getTime() - now.getTime()) / (1000 * 60 * 60)), 'ساعات');

        return correctedTime;
    } catch (error) {
        console.error('❌ خطأ في الحصول على وقت المدينة:', error);
        return new Date(); // العودة للوقت المحلي في حالة الخطأ
    }
}

// دالة لتحديث الساعة التناظرية
function updateAnalogClock() {
    const cityTime = getCurrentTimeForSelectedCity();
    const hours = cityTime.getHours() % 12;
    const minutes = cityTime.getMinutes();
    const seconds = cityTime.getSeconds();

    const hourHand = document.querySelector('.hour-hand');
    const minuteHand = document.querySelector('.minute-hand');
    const secondHand = document.querySelector('.second-hand');

    if (hourHand && minuteHand && secondHand) {
        const hourDeg = (hours * 30) + (minutes * 0.5);
        const minuteDeg = (minutes * 6) + (seconds * 0.1);
        const secondDeg = seconds * 6;

        hourHand.style.transform = `rotate(${hourDeg}deg)`;
        minuteHand.style.transform = `rotate(${minuteDeg}deg)`;
        secondHand.style.transform = `rotate(${secondDeg}deg)`;
    }
}

// دالة لتحديث الساعة الرقمية
function updateDigitalClock() {
    const cityTime = getCurrentTimeForSelectedCity();
    const hours = cityTime.getHours();
    const minutes = cityTime.getMinutes();
    const seconds = cityTime.getSeconds();

    const timeFormat = localStorage.getItem('timeFormat') || '24';
    let displayHours = hours;
    let ampm = '';

    if (timeFormat === '12') {
        displayHours = hours % 12 || 12;
        ampm = hours < 12 ? ' صباحًا' : ' مساءً';
    }

    const timeString = `${String(displayHours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}${ampm}`;

    const digitalClock = document.querySelector('.digital-clock');
    if (digitalClock) {
        digitalClock.textContent = timeString;
    }
}

// دالة لتحديث التاريخ
function updateDate() {
    const cityTime = getCurrentTimeForSelectedCity();

    // التاريخ الميلادي
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    const gregorianDate = cityTime.toLocaleDateString('ar-SA', options);

    // التاريخ الهجري (باستخدام moment-hijri)
    let hijriDate = '';
    if (window.moment && window.moment.isMoment) {
        const hijri = moment(cityTime).format('iYYYY/iM/iD');
        const hijriParts = hijri.split('/');
        const hijriYear = hijriParts[0];
        const hijriMonth = getHijriMonthName(parseInt(hijriParts[1]));
        const hijriDay = hijriParts[2];
        hijriDate = `${hijriDay} ${hijriMonth} ${hijriYear} هـ`;
    }

    // تحديث العناصر في الصفحة
    const gregorianElement = document.querySelector('.gregorian-date');
    const hijriElement = document.querySelector('.hijri-date');

    if (gregorianElement) {
        gregorianElement.textContent = gregorianDate;
    }

    if (hijriElement) {
        hijriElement.textContent = hijriDate;
    }
}

// دالة للحصول على اسم الشهر الهجري
function getHijriMonthName(month) {
    const hijriMonths = [
        'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني',
        'جمادى الأولى', 'جمادى الآخرة', 'رجب', 'شعبان',
        'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
    ];
    return hijriMonths[month - 1];
}

// دالة لتحديث العد التنازلي للصلاة القادمة
function updateCountdown() {
    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    const times = window.prayerTimes[currentCity];

    if (!times) {
        console.error('مواقيت الصلاة غير متوفرة لتحديث العد التنازلي');
        return;
    }

    const cityTime = getCurrentTimeForSelectedCity();
    const currentHours = cityTime.getHours();
    const currentMinutes = cityTime.getMinutes();
    const currentTimeInMinutes = currentHours * 60 + currentMinutes;
    
    // ترتيب الصلوات
    const prayers = [
        { name: 'الفجر', time: times.fajr, id: 'fajr' },
        { name: 'الشروق', time: times.sunrise, id: 'sunrise' },
        { name: 'الظهر', time: times.dhuhr, id: 'dhuhr' },
        { name: 'العصر', time: times.asr, id: 'asr' },
        { name: 'المغرب', time: times.maghrib, id: 'maghrib' },
        { name: 'العشاء', time: times.isha, id: 'isha' }
    ];
    
    // تحويل وقت الصلاة إلى دقائق للمقارنة
    prayers.forEach(prayer => {
        if (prayer.time) {
            const [hours, minutes] = prayer.time.split(':').map(Number);
            prayer.timeInMinutes = hours * 60 + minutes;
        } else {
            prayer.timeInMinutes = -1; // قيمة غير صالحة إذا كان الوقت غير متاح
        }
    });
    
    // البحث عن الصلاة القادمة
    let nextPrayer = null;
    let minTimeToNextPrayer = Infinity;
    
    for (const prayer of prayers) {
        if (!prayer.time) continue;
        
        let timeToNextPrayer = prayer.timeInMinutes - currentTimeInMinutes;
        
        // إذا كان الوقت سالباً (أي أن الصلاة قد مرت)، أضف 24 ساعة
        if (timeToNextPrayer < 0) {
            timeToNextPrayer += 24 * 60; // 24 ساعة بالدقائق
        }
        
        // إذا كان هذا أقرب صلاة قادمة
        if (timeToNextPrayer < minTimeToNextPrayer) {
            minTimeToNextPrayer = timeToNextPrayer;
            nextPrayer = prayer;
        }
    }
    
    // التأكد من وجود صلاة قادمة
    if (!nextPrayer) {
        console.error('لم يتم العثور على صلاة قادمة!');
        nextPrayer = prayers[0]; // استخدام الفجر كاحتياطي
    }
    
    // تحديث نص الصلاة القادمة
    const nextPrayerText = document.querySelector('.next-prayer-text');
    if (nextPrayerText) {
        nextPrayerText.textContent = `ننتظر صلاة ${nextPrayer.name}`;
    }
    
    // تحديث العد التنازلي
    const countdownTime = document.querySelector('.countdown-time');
    if (countdownTime) {
        const hours = Math.floor(minTimeToNextPrayer / 60);
        const minutes = minTimeToNextPrayer % 60;
        countdownTime.textContent = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
    }
    
    return nextPrayer;
}

// دالة لبدء العد التنازلي للإقامة
function startIqamahCountdown(prayerName, arabicName) {
    // إلغاء أي مؤقت سابق للإقامة
    if (iqamahTimer) {
        clearInterval(iqamahTimer);
        iqamahTimer = null;
    }
    
    // الحصول على مدة الإقامة بالدقائق
    const iqamahMinutes = window.iqamahTimes[prayerName] || 10;
    
    // تحويل إلى ثواني
    let secondsLeft = iqamahMinutes * 60;
    
    // عرض العد التنازلي
    const iqamahDisplay = document.querySelector('.iqamah-countdown');
    const iqamahLabel = document.getElementById('iqamah-label');
    const iqamahTime = document.getElementById('iqamah-time');
    const iqamahPrayerName = document.getElementById('iqamah-prayer-name');
    
    // تأكد من أن العناصر موجودة
    if (!iqamahDisplay || !iqamahLabel || !iqamahTime || !iqamahPrayerName) {
        console.error('عناصر العد التنازلي للإقامة غير موجودة');
        return;
    }
    
    // إظهار العد التنازلي في وسط الشاشة
    iqamahDisplay.style.display = 'flex';
    iqamahLabel.textContent = 'الإقامة';
    iqamahPrayerName.textContent = `صلاة ${arabicName}`;
    
    // إنشاء عنصر التعتيم
    let darknessOverlay = document.getElementById('darkness-overlay');
    if (!darknessOverlay) {
        darknessOverlay = document.createElement('div');
        darknessOverlay.id = 'darkness-overlay';
        darknessOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: black;
            z-index: 9998;
            display: block;
            opacity: 0;
            transition: opacity 1s ease;
        `;
        document.body.appendChild(darknessOverlay);
    }
    
    // تفعيل التعتيم عند بدء العد التنازلي للإقامة
    darknessOverlay.style.display = 'block';
    darknessOverlay.style.opacity = '0.9';
    
    // تحديث العد التنازلي كل ثانية
    iqamahTimer = setInterval(() => {
        secondsLeft--;
        
        const minutes = Math.floor(secondsLeft / 60);
        const seconds = secondsLeft % 60;
        
        // تحديث العد التنازلي بخط كبير وواضح
        iqamahTime.textContent = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
        
        if (secondsLeft <= 0) {
            clearInterval(iqamahTimer);
            iqamahTimer = null;
            
            // تشغيل صوت الإقامة
            const iqamaAudio = document.getElementById('iqama-audio');
            if (iqamaAudio) {
                iqamaAudio.play()
                    .then(() => {
                        console.log('بدأ تشغيل صوت الإقامة');
                        startDarknessPeriod(prayerName, darknessOverlay, iqamahDisplay);
                    })
                    .catch(err => {
                        console.error('خطأ في تشغيل صوت الإقامة:', err);
                        startDarknessPeriod(prayerName, darknessOverlay, iqamahDisplay);
                    });
            } else {
                console.error('عنصر صوت الإقامة غير موجود');
                startDarknessPeriod(prayerName, darknessOverlay, iqamahDisplay);
            }
        }
    }, 1000);
}

// دالة لبدء فترة التعتيم
function startDarknessPeriod(prayerName, darknessOverlay, iqamahDisplay) {
    // الحصول على مدة التعتيم المحفوظة من مصادر متعددة
    let darknessMinutes = 10; // القيمة الافتراضية
    
    try {
        // محاولة قراءة من حقول الإدخال مباشرة أولاً
        let inputElement = document.getElementById(`${prayerName}-darkness`);
        if (!inputElement) {
            inputElement = document.getElementById(`darkness-duration-${prayerName}`);
        }
        
        if (inputElement && inputElement.value.trim() !== '') {
            const inputValue = parseInt(inputElement.value) || 0;
            if (inputValue >= 0) {
                darknessMinutes = inputValue;
                console.log(`تم قراءة مدة التعتيم من حقل الإدخال: ${darknessMinutes} دقيقة`);
            }
        } else {
            // محاولة قراءة من التخزين المحلي الجديد
            const savedDarknessTimes = JSON.parse(localStorage.getItem('darknessTimes') || '{}');
            if (savedDarknessTimes[prayerName] !== undefined) {
                darknessMinutes = parseInt(savedDarknessTimes[prayerName]) || 0;
                console.log(`تم قراءة مدة التعتيم من التخزين الجديد: ${darknessMinutes} دقيقة`);
            } else if (window.darknessTimes && window.darknessTimes[prayerName] !== undefined) {
                // محاولة قراءة من المتغير العام
                darknessMinutes = parseInt(window.darknessTimes[prayerName]) || 0;
                console.log(`تم قراءة مدة التعتيم من المتغير العام: ${darknessMinutes} دقيقة`);
            } else {
                // محاولة قراءة من التخزين المحلي القديم
                const localStorageKey = `darknessDuration_${prayerName}`;
                const savedDuration = localStorage.getItem(localStorageKey);
                if (savedDuration) {
                    darknessMinutes = parseInt(savedDuration) || 0;
                    console.log(`تم قراءة مدة التعتيم من التخزين القديم: ${darknessMinutes} دقيقة`);
                }
            }
        }
    } catch (error) {
        console.error('خطأ في قراءة مدة التعتيم:', error);
        darknessMinutes = 10; // استخدام القيمة الافتراضية
    }
    
    console.log(`مدة التعتيم النهائية لصلاة ${prayerName}: ${darknessMinutes} دقيقة`);
    
    // إخفاء العد التنازلي للإقامة
    iqamahDisplay.style.display = 'none';
    
    // إنشاء عنصر الساعة الرقمية في وسط الشاشة
    let digitalClockOverlay = document.getElementById('digital-clock-overlay');
    if (!digitalClockOverlay) {
        digitalClockOverlay = document.createElement('div');
        digitalClockOverlay.id = 'digital-clock-overlay';
        digitalClockOverlay.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 56px;
            color: #40E0D0;
            font-weight: bold;
            text-align: center;
            z-index: 10000;
            text-shadow: 0 0 15px rgba(64, 224, 208, 0.7);
        `;
        document.body.appendChild(digitalClockOverlay);
    }
    
    // تحديث الساعة الرقمية كل ثانية
    const updateDigitalClockOverlay = () => {
        const cityTime = getCurrentTimeForSelectedCity();
        const hours = String(cityTime.getHours()).padStart(2, '0');
        const minutes = String(cityTime.getMinutes()).padStart(2, '0');
        const seconds = String(cityTime.getSeconds()).padStart(2, '0');
        digitalClockOverlay.textContent = `${hours}:${minutes}:${seconds}`;
    };
    
    // تحديث الساعة مباشرة ثم كل ثانية
    updateDigitalClockOverlay();
    digitalClockInterval = setInterval(updateDigitalClockOverlay, 1000);
    
    // إخفاء التعتيم والساعة الرقمية بعد المدة المحددة
    darknessTimer = setTimeout(() => {
        // إخفاء التعتيم
        darknessOverlay.style.opacity = '0';
        setTimeout(() => {
            darknessOverlay.style.display = 'none';
        }, 1000);
        
        // إيقاف تحديث الساعة الرقمية وإزالتها
        clearInterval(digitalClockInterval);
        digitalClockInterval = null;
        if (digitalClockOverlay) {
            digitalClockOverlay.remove();
        }
    }, darknessMinutes * 60 * 1000);
}

// دالة لتحميل مدة الإقامة المحفوظة
function loadIqamahTimes() {
    const savedTimes = localStorage.getItem('iqamahTimes');
    if (savedTimes) {
        window.iqamahTimes = JSON.parse(savedTimes);
        
        // تحديث قيم حقول الإدخال
        document.getElementById('fajr-iqama-duration').value = window.iqamahTimes.fajr || 10;
        document.getElementById('dhuhr-iqama-duration').value = window.iqamahTimes.dhuhr || 10;
        document.getElementById('asr-iqama-duration').value = window.iqamahTimes.asr || 10;
        document.getElementById('maghrib-iqama-duration').value = window.iqamahTimes.maghrib || 10;
        document.getElementById('isha-iqama-duration').value = window.iqamahTimes.isha || 10;
    }
}

// دالة لتحميل مدة التعتيم المحفوظة
function loadDarknessTimes() {
    const savedTimes = localStorage.getItem('darknessTimes');
    if (savedTimes) {
        window.darknessTimes = JSON.parse(savedTimes);
        
        // تحديث قيم حقول الإدخال
        document.getElementById('fajr-darkness').value = window.darknessTimes.fajr || 10;
        document.getElementById('dhuhr-darkness').value = window.darknessTimes.dhuhr || 10;
        document.getElementById('asr-darkness').value = window.darknessTimes.asr || 10;
        document.getElementById('maghrib-darkness').value = window.darknessTimes.maghrib || 10;
        document.getElementById('isha-darkness').value = window.darknessTimes.isha || 10;
    }
}

// دالة لتحديث المنطقة الزمنية الحالية
function updateCurrentTimezone(cityKey) {
    try {
        console.log(`تحديث المنطقة الزمنية للمدينة: ${cityKey}`);

        // حفظ المدينة الجديدة
        localStorage.setItem('selectedCity', cityKey);

        // تحديث فوري لجميع الساعات
        updateAllClocksOnSettingsChange();

        console.log('تم تحديث المنطقة الزمنية بنجاح');
    } catch (error) {
        console.error('خطأ في تحديث المنطقة الزمنية:', error);
    }
}

// دالة لتحديث جميع الساعات عند تغيير المدينة أو الدالة
function updateAllClocksOnSettingsChange() {
    console.log('تحديث جميع الساعات بسبب تغيير الإعدادات...');

    // تحديث الساعة التناظرية
    updateAnalogClock();

    // تحديث الساعة الرقمية
    updateDigitalClock();

    // تحديث التاريخ
    updateDate();

    // تحديث العد التنازلي
    updateCountdown();

    console.log('تم تحديث جميع الساعات بنجاح');
}

// دالة لبدء مراقبة تغييرات الإعدادات
function startSettingsChangeMonitoring() {
    console.log('بدء مراقبة تغييرات الإعدادات...');
    
    // مراقبة تغيير المدينة
    const citySelect = document.getElementById('city-select');
    if (citySelect) {
        citySelect.addEventListener('change', function() {
            console.log('تم تغيير المدينة، تحديث الساعات...');
            setTimeout(updateAllClocksOnSettingsChange, 100);
        });
    }
    
    // مراقبة تغيير طريقة الحساب
    const methodSelect = document.getElementById('method-select');
    if (methodSelect) {
        methodSelect.addEventListener('change', function() {
            console.log('تم تغيير طريقة الحساب، تحديث الساعات...');
            setTimeout(updateAllClocksOnSettingsChange, 100);
        });
    }
    
    // مراقبة تغيير المذهب
    const madhabSelect = document.getElementById('madhab-select');
    if (madhabSelect) {
        madhabSelect.addEventListener('change', function() {
            console.log('تم تغيير المذهب، تحديث الساعات...');
            setTimeout(updateAllClocksOnSettingsChange, 100);
        });
    }
    
    // مراقبة تغيير الدولة
    const countrySelect = document.getElementById('country-select');
    if (countrySelect) {
        countrySelect.addEventListener('change', function() {
            console.log('تم تغيير الدولة، تحديث الساعات...');
            setTimeout(updateAllClocksOnSettingsChange, 100);
        });
    }
    
    console.log('تم إعداد مراقبة تغييرات الإعدادات بنجاح');
}

// دالة لبدء تشغيل الساعات
function startClocks() {
    console.log('بدء تشغيل الساعات...');
    
    // تحديث أولي
    updateAnalogClock();
    updateDigitalClock();
    updateDate();
    updateCountdown();
    
    // بدء التحديث المستمر
    setInterval(updateAnalogClock, 1000);
    setInterval(updateDigitalClock, 1000);
    setInterval(updateDate, 60000); // تحديث التاريخ كل دقيقة
    setInterval(updateCountdown, 1000);
    
    // بدء مراقبة تغييرات الإعدادات
    startSettingsChangeMonitoring();
    
    console.log('تم بدء تشغيل الساعات بنجاح');
}

// تصدير الدوال
window.getCurrentTimeForSelectedCity = getCurrentTimeForSelectedCity;
window.updateCurrentTimezone = updateCurrentTimezone;
window.updateAnalogClock = updateAnalogClock;
window.updateDigitalClock = updateDigitalClock;
window.updateDate = updateDate;
window.updateCountdown = updateCountdown;
window.startIqamahCountdown = startIqamahCountdown;
window.loadIqamahTimes = loadIqamahTimes;
window.loadDarknessTimes = loadDarknessTimes;
window.updateAllClocksOnSettingsChange = updateAllClocksOnSettingsChange;
window.startClocks = startClocks;

// بدء تشغيل الساعات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تحميل ملف الساعة الجديد...');
    setTimeout(startClocks, 1000);
});
