<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تغيير المدينة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        select, button {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .clock {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 15px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .log {
            height: 200px;
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار تغيير المدينة</h1>
        
        <div class="test-section">
            <h3>اختيار المدينة</h3>
            <select id="test-city-select">
                <option value="Asia/Amman">عمان، الأردن</option>
                <option value="Asia/Riyadh">الرياض، السعودية</option>
                <option value="Asia/Dubai">دبي، الإمارات</option>
                <option value="Africa/Cairo">القاهرة، مصر</option>
                <option value="Asia/Jerusalem">القدس، فلسطين</option>
            </select>
            <button onclick="testCityChange()">تغيير المدينة</button>
            <button onclick="testDirectUpdate()">تحديث مباشر</button>
        </div>

        <div class="test-section">
            <h3>الساعة الحالية</h3>
            <div id="current-time" class="clock">--:--:--</div>
            <div id="current-date" style="text-align: center;">--</div>
            <div id="current-timezone" style="text-align: center; color: #666; margin-top: 10px;">المنطقة الزمنية: --</div>
        </div>

        <div class="test-section">
            <h3>سجل الأحداث</h3>
            <div id="event-log" class="log"></div>
            <button onclick="clearLog()">مسح السجل</button>
        </div>
    </div>

    <script>
        let eventLog = [];
        let currentTimezone = 'Asia/Amman';

        // دالة لإضافة رسالة إلى السجل
        function addToLog(message) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            eventLog.push(`[${timestamp}] ${message}`);
            
            const logElement = document.getElementById('event-log');
            if (logElement) {
                logElement.innerHTML = eventLog.slice(-15).join('<br>');
                logElement.scrollTop = logElement.scrollHeight;
            }
        }

        // دالة للحصول على الوقت حسب المنطقة الزمنية
        function getTimeByTimezone(timezone) {
            try {
                const now = new Date();
                return new Date(now.toLocaleString("en-US", {timeZone: timezone}));
            } catch (error) {
                addToLog(`خطأ في تحويل المنطقة الزمنية: ${error.message}`);
                return new Date();
            }
        }

        // دالة لتحديث عرض الساعة
        function updateClockDisplay() {
            try {
                const now = getTimeByTimezone(currentTimezone);
                const timeString = now.toLocaleTimeString('ar-SA');
                const dateString = now.toLocaleDateString('ar-SA');
                
                document.getElementById('current-time').textContent = timeString;
                document.getElementById('current-date').textContent = dateString;
                document.getElementById('current-timezone').textContent = `المنطقة الزمنية: ${currentTimezone}`;
                
            } catch (error) {
                addToLog(`خطأ في تحديث عرض الساعة: ${error.message}`);
            }
        }

        // دالة لتحديث المنطقة الزمنية
        function updateCurrentTimezone(cityKey) {
            try {
                addToLog(`تحديث المنطقة الزمنية للمدينة: ${cityKey}`);
                
                // تحديث المنطقة الزمنية
                currentTimezone = cityKey;
                
                // تحديث العرض
                updateClockDisplay();
                
                addToLog(`تم تحديث المنطقة الزمنية إلى: ${currentTimezone}`);
                
                return currentTimezone;
            } catch (error) {
                addToLog(`خطأ في تحديث المنطقة الزمنية: ${error.message}`);
                return currentTimezone;
            }
        }

        // دالة اختبار تغيير المدينة
        function testCityChange() {
            const citySelect = document.getElementById('test-city-select');
            const selectedCity = citySelect.value;
            
            addToLog(`اختبار تغيير المدينة إلى: ${selectedCity}`);
            
            // حفظ المدينة الجديدة
            localStorage.setItem('selectedCity', selectedCity);
            
            // تحديث المنطقة الزمنية
            updateCurrentTimezone(selectedCity);
            
            addToLog(`تم حفظ المدينة الجديدة: ${selectedCity}`);
        }

        // دالة اختبار التحديث المباشر
        function testDirectUpdate() {
            addToLog('اختبار التحديث المباشر...');
            updateClockDisplay();
            addToLog('تم التحديث المباشر');
        }

        // دالة مسح السجل
        function clearLog() {
            eventLog = [];
            document.getElementById('event-log').innerHTML = '';
            addToLog('تم مسح السجل');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addToLog('تم تحميل صفحة الاختبار');
            
            // تحميل المدينة المحفوظة
            const savedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
            currentTimezone = savedCity;
            
            // تعيين القيمة في القائمة
            const citySelect = document.getElementById('test-city-select');
            if (citySelect) {
                citySelect.value = savedCity;
            }
            
            // تحديث أولي
            updateClockDisplay();
            
            // تحديث دوري كل ثانية
            setInterval(updateClockDisplay, 1000);
            
            addToLog(`تم تهيئة الصفحة بالمدينة: ${savedCity}`);
        });

        // جعل الدوال متاحة عالمياً
        window.updateCurrentTimezone = updateCurrentTimezone;
        window.getTimeByTimezone = getTimeByTimezone;
    </script>
</body>
</html>
