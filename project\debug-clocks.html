<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - اختبار دوال الساعة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .debug-info {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Debug - اختبار دوال الساعة</h1>
    
    <div class="debug-info">
        <h3>حالة تحميل الملفات:</h3>
        <div id="file-status"></div>
    </div>
    
    <div class="debug-info">
        <h3>الدوال المتاحة:</h3>
        <div id="functions-status"></div>
    </div>
    
    <div class="debug-info">
        <h3>اختبار الدوال:</h3>
        <button onclick="testFunctions()">اختبار الدوال</button>
        <button onclick="testTimezone()">اختبار المنطقة الزمنية</button>
        <button onclick="changeCity()">تغيير المدينة</button>
        <div id="test-results"></div>
    </div>

    <script src="clock-new.js"></script>
    <script>
        function checkFileStatus() {
            const statusDiv = document.getElementById('file-status');
            let html = '';
            
            // فحص وجود الدوال
            const functions = [
                'getCurrentTimeByTimezone',
                'updateAnalogClock',
                'updateDigitalClock',
                'updateDate',
                'updateCountdown',
                'updateAllClocks',
                'setupClockEventListeners'
            ];
            
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    html += `<div class="success">✅ ${func} - متاح</div>`;
                } else {
                    html += `<div class="error">❌ ${func} - غير متاح</div>`;
                }
            });
            
            statusDiv.innerHTML = html;
        }
        
        function testFunctions() {
            const resultsDiv = document.getElementById('test-results');
            let html = '<h4>نتائج الاختبار:</h4>';
            
            try {
                // اختبار getCurrentTimeByTimezone
                if (typeof getCurrentTimeByTimezone === 'function') {
                    const time = getCurrentTimeByTimezone();
                    html += `<div class="success">✅ getCurrentTimeByTimezone() = ${time}</div>`;
                } else {
                    html += `<div class="error">❌ getCurrentTimeByTimezone غير متاح</div>`;
                }
                
                // اختبار updateAllClocks
                if (typeof updateAllClocks === 'function') {
                    updateAllClocks();
                    html += `<div class="success">✅ updateAllClocks() تم تنفيذها بنجاح</div>`;
                } else {
                    html += `<div class="error">❌ updateAllClocks غير متاح</div>`;
                }
                
            } catch (error) {
                html += `<div class="error">❌ خطأ: ${error.message}</div>`;
            }
            
            resultsDiv.innerHTML = html;
        }
        
        function testTimezone() {
            const resultsDiv = document.getElementById('test-results');
            let html = '<h4>اختبار المنطقة الزمنية:</h4>';
            
            try {
                const cities = ['Asia/Amman', 'Asia/Dubai', 'Asia/Riyadh'];
                
                cities.forEach(city => {
                    localStorage.setItem('selectedCity', city);
                    const time = getCurrentTimeByTimezone();
                    html += `<div class="success">✅ ${city}: ${time}</div>`;
                });
                
                // إعادة تعيين المدينة الافتراضية
                localStorage.setItem('selectedCity', 'Asia/Amman');
                
            } catch (error) {
                html += `<div class="error">❌ خطأ: ${error.message}</div>`;
            }
            
            resultsDiv.innerHTML = html;
        }
        
        function changeCity() {
            const resultsDiv = document.getElementById('test-results');
            let html = '<h4>اختبار تغيير المدينة:</h4>';
            
            try {
                // تغيير المدينة
                localStorage.setItem('selectedCity', 'Asia/Dubai');
                html += `<div class="success">✅ تم تغيير المدينة إلى دبي</div>`;
                
                // تحديث الساعات
                if (typeof updateAllClocks === 'function') {
                    updateAllClocks();
                    html += `<div class="success">✅ تم تحديث الساعات</div>`;
                }
                
                // إعادة تعيين المدينة
                localStorage.setItem('selectedCity', 'Asia/Amman');
                
            } catch (error) {
                html += `<div class="error">❌ خطأ: ${error.message}</div>`;
            }
            
            resultsDiv.innerHTML = html;
        }
        
        // فحص الحالة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkFileStatus, 1000);
        });
    </script>
</body>
</html> 