<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ساعة المسجد - نسخة مبسطة</title>
    <style>
        :root {
            --gold-color: #71d3ee;
            --dark-pink: #4a3b3b;
            --light-gray: #faf5f5;
            --dark-gray: #333333;
            --text-color: white;
            --text-size: 24px;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background-color: #fffbfb;
            direction: rtl;
            display: flex;
            height: 100vh;
        }

        .vertical-panel {
            width: 5cm;
            height: 100vh;
            background-color: var(--dark-pink);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 5px;
            box-sizing: border-box;
            position: fixed;
            top: 0;
            right: 0;
            box-shadow: -2px 0 5px #fcf9f9;
            color: var(--gold-color);
            justify-content: flex-start;
            padding-top: 5px;
            overflow-y: auto;
        }

        .prayer-times {
            width: calc(100% - 5cm);
            height: 3cm;
            background-color: var(--dark-pink);
            color: var(--gold-color);
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 10px 0;
            position: fixed;
            bottom: 0;
            left: 0;
            z-index: 5;
            box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.2);
        }

        .prayer-time {
            text-align: center;
            padding: 10px;
        }

        .prayer-name {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #40E0D0;
        }

        .prayer-hour {
            font-size: 1.7em;
            margin-top: 10px;
            color: #40E0D0;
        }

        .main-content {
            margin-right: 5cm;
            margin-bottom: 3cm;
            padding: 20px;
            width: calc(100% - 5cm);
            height: calc(100vh - 3cm);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .clock-container {
            text-align: center;
            margin: 20px;
        }

        .digital-clock {
            font-size: 4em;
            font-weight: bold;
            color: var(--dark-pink);
            margin: 20px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .date-display {
            font-size: 1.5em;
            color: var(--dark-pink);
            margin: 10px 0;
        }

        .city-selector {
            margin: 20px;
            padding: 10px;
            background: var(--dark-pink);
            border-radius: 10px;
            color: var(--gold-color);
        }

        .city-selector select {
            padding: 5px 10px;
            margin: 5px;
            border-radius: 5px;
            border: none;
            background: var(--gold-color);
            color: var(--dark-pink);
        }

        .status {
            padding: 10px;
            margin: 10px;
            border-radius: 5px;
            font-weight: bold;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <!-- اللوحة الجانبية -->
    <div class="vertical-panel">
        <h3>ساعة المسجد</h3>
        <div id="status-display"></div>
    </div>
    
    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <div class="clock-container">
            <div class="digital-clock" id="digital-clock">00:00:00</div>
            <div class="date-display" id="date-display">--/--/----</div>
        </div>
        
        <div class="city-selector">
            <label>اختر المدينة:</label>
            <select id="city-select">
                <option value="Amman">عمان</option>
                <option value="Dubai">دبي</option>
                <option value="Riyadh">الرياض</option>
                <option value="Jerusalem">القدس</option>
            </select>
        </div>
        
        <div id="test-results"></div>
    </div>
    
    <!-- شريط مواقيت الصلاة -->
    <div class="prayer-times">
        <div class="prayer-time">
            <div class="prayer-name">الفجر</div>
            <div class="prayer-hour" id="fajr-time">--:--</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">الشروق</div>
            <div class="prayer-hour" id="sunrise-time">--:--</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">الظهر</div>
            <div class="prayer-hour" id="dhuhr-time">--:--</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">العصر</div>
            <div class="prayer-hour" id="asr-time">--:--</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">المغرب</div>
            <div class="prayer-hour" id="maghrib-time">--:--</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">العشاء</div>
            <div class="prayer-hour" id="isha-time">--:--</div>
        </div>
    </div>

    <!-- تحميل مكتبة PrayTimes -->
    <script src="js/PrayTimes.js"></script>

    <script>
        // بيانات المدن
        const cities = {
            'Amman': { lat: 31.9539, lng: 35.9106, timezone: 3, name: 'عمان' },
            'Dubai': { lat: 25.2048, lng: 55.2708, timezone: 4, name: 'دبي' },
            'Riyadh': { lat: 24.7136, lng: 46.6753, timezone: 3, name: 'الرياض' },
            'Jerusalem': { lat: 31.7683, lng: 35.2137, timezone: 2, name: 'القدس' }
        };

        // تهيئة مكتبة PrayTimes
        let prayTimes;
        
        function initializePrayTimes() {
            try {
                if (typeof PrayTimes !== 'undefined') {
                    prayTimes = new PrayTimes('MWL');
                    updateStatus('تم تهيئة مكتبة PrayTimes بنجاح', 'success');
                    return true;
                } else {
                    updateStatus('مكتبة PrayTimes غير متوفرة', 'error');
                    return false;
                }
            } catch (error) {
                updateStatus('خطأ في تهيئة PrayTimes: ' + error.message, 'error');
                return false;
            }
        }

        // دالة لتحديث الحالة
        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status-display');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
            console.log(message);
        }

        // دالة لتحديث الساعة الرقمية
        function updateDigitalClock() {
            const clockDiv = document.getElementById('digital-clock');
            const dateDiv = document.getElementById('date-display');
            
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            const dateString = now.toLocaleDateString('ar-SA');
            
            clockDiv.textContent = timeString;
            dateDiv.textContent = dateString;
        }

        // دالة لحساب مواقيت الصلاة
        function calculatePrayerTimes(cityName) {
            if (!prayTimes) {
                updateStatus('مكتبة PrayTimes غير مهيأة', 'error');
                return;
            }

            try {
                const city = cities[cityName];
                if (!city) {
                    updateStatus('مدينة غير معروفة: ' + cityName, 'error');
                    return;
                }

                const coordinates = [city.lat, city.lng];
                const date = new Date();
                const times = prayTimes.getTimes(date, coordinates, city.timezone);

                // تحديث مواقيت الصلاة
                const prayerElements = {
                    'fajr': document.getElementById('fajr-time'),
                    'sunrise': document.getElementById('sunrise-time'),
                    'dhuhr': document.getElementById('dhuhr-time'),
                    'asr': document.getElementById('asr-time'),
                    'maghrib': document.getElementById('maghrib-time'),
                    'isha': document.getElementById('isha-time')
                };

                for (let prayer in prayerElements) {
                    if (prayerElements[prayer] && times[prayer] && times[prayer] !== '-----') {
                        prayerElements[prayer].textContent = times[prayer];
                    }
                }

                updateStatus(`تم حساب مواقيت الصلاة لمدينة ${city.name}`, 'success');
                
            } catch (error) {
                updateStatus('خطأ في حساب مواقيت الصلاة: ' + error.message, 'error');
            }
        }

        // دالة لتغيير المدينة
        function changeCity() {
            const citySelect = document.getElementById('city-select');
            const selectedCity = citySelect.value;
            
            updateStatus(`تم تغيير المدينة إلى: ${cities[selectedCity].name}`, 'info');
            calculatePrayerTimes(selectedCity);
        }

        // إضافة مستمعي الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('جاري تحميل التطبيق...', 'info');
            
            // تهيئة PrayTimes
            if (initializePrayTimes()) {
                // تحديث الساعة كل ثانية
                setInterval(updateDigitalClock, 1000);
                
                // حساب مواقيت الصلاة للمدينة الافتراضية
                const defaultCity = document.getElementById('city-select').value;
                calculatePrayerTimes(defaultCity);
                
                // إضافة مستمع لتغيير المدينة
                document.getElementById('city-select').addEventListener('change', changeCity);
                
                updateStatus('التطبيق جاهز للاستخدام', 'success');
            }
        });
    </script>
</body>
</html> 