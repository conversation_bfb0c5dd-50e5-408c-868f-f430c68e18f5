<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص وحدة التحكم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>فحص وحدة التحكم والأخطاء</h1>
    
    <div class="debug-section">
        <h3>1. فحص تحميل الملفات:</h3>
        <button onclick="checkFileLoading()">فحص تحميل الملفات</button>
        <div id="file-status"></div>
    </div>
    
    <div class="debug-section">
        <h3>2. فحص المكتبات:</h3>
        <button onclick="checkLibraries()">فحص المكتبات</button>
        <div id="library-status"></div>
    </div>
    
    <div class="debug-section">
        <h3>3. فحص الأخطاء في وحدة التحكم:</h3>
        <button onclick="captureConsoleErrors()">التقاط الأخطاء</button>
        <div id="console-output"></div>
    </div>
    
    <div class="debug-section">
        <h3>4. اختبار بسيط:</h3>
        <button onclick="simpleTest()">اختبار بسيط</button>
        <div id="simple-test-result"></div>
    </div>

    <script>
        let consoleOutput = '';
        let originalConsoleError = console.error;
        let originalConsoleLog = console.log;
        let originalConsoleWarn = console.warn;

        // اعتراض رسائل وحدة التحكم
        function interceptConsole() {
            console.error = function(...args) {
                consoleOutput += 'ERROR: ' + args.join(' ') + '\n';
                originalConsoleError.apply(console, args);
            };
            
            console.log = function(...args) {
                consoleOutput += 'LOG: ' + args.join(' ') + '\n';
                originalConsoleLog.apply(console, args);
            };
            
            console.warn = function(...args) {
                consoleOutput += 'WARN: ' + args.join(' ') + '\n';
                originalConsoleWarn.apply(console, args);
            };
        }

        // فحص تحميل الملفات
        function checkFileLoading() {
            const statusDiv = document.getElementById('file-status');
            let html = '';
            
            // فحص وجود الملفات
            const files = [
                'js/PrayTimes.js',
                'clock-new.js',
                'prayer-times-new.js',
                'prayer-manager.js'
            ];
            
            files.forEach(file => {
                const script = document.createElement('script');
                script.src = file;
                script.onload = function() {
                    html += `<div class="success">✅ ${file} - تم التحميل</div>`;
                    statusDiv.innerHTML = html;
                };
                script.onerror = function() {
                    html += `<div class="error">❌ ${file} - فشل التحميل</div>`;
                    statusDiv.innerHTML = html;
                };
                document.head.appendChild(script);
            });
        }

        // فحص المكتبات
        function checkLibraries() {
            const statusDiv = document.getElementById('library-status');
            let html = '';
            
            const libraries = [
                { name: 'PrayTimes', check: () => typeof PrayTimes !== 'undefined' },
                { name: 'window.prayTimes', check: () => typeof window.prayTimes !== 'undefined' },
                { name: 'localStorage', check: () => typeof localStorage !== 'undefined' },
                { name: 'Date', check: () => typeof Date !== 'undefined' }
            ];
            
            libraries.forEach(lib => {
                if (lib.check()) {
                    html += `<div class="success">✅ ${lib.name} - متوفر</div>`;
                } else {
                    html += `<div class="error">❌ ${lib.name} - غير متوفر</div>`;
                }
            });
            
            statusDiv.innerHTML = html;
        }

        // التقاط أخطاء وحدة التحكم
        function captureConsoleErrors() {
            const outputDiv = document.getElementById('console-output');
            outputDiv.textContent = consoleOutput || 'لا توجد أخطاء مسجلة';
        }

        // اختبار بسيط
        function simpleTest() {
            const resultDiv = document.getElementById('simple-test-result');
            let html = '';
            
            try {
                // اختبار 1: إنشاء كائن Date
                const now = new Date();
                html += `<div class="success">✅ Date API يعمل: ${now.toLocaleString('ar-SA')}</div>`;
                
                // اختبار 2: localStorage
                localStorage.setItem('test', 'test-value');
                const testValue = localStorage.getItem('test');
                if (testValue === 'test-value') {
                    html += `<div class="success">✅ localStorage يعمل</div>`;
                } else {
                    html += `<div class="error">❌ localStorage لا يعمل</div>`;
                }
                localStorage.removeItem('test');
                
                // اختبار 3: PrayTimes
                if (typeof PrayTimes !== 'undefined') {
                    const pt = new PrayTimes('MWL');
                    html += `<div class="success">✅ PrayTimes يعمل</div>`;
                } else {
                    html += `<div class="error">❌ PrayTimes غير متوفر</div>`;
                }
                
            } catch (error) {
                html += `<div class="error">❌ خطأ في الاختبار: ${error.message}</div>`;
            }
            
            resultDiv.innerHTML = html;
        }

        // تشغيل عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('بدء فحص وحدة التحكم...');
            interceptConsole();
            checkLibraries();
            simpleTest();
        });
    </script>
</body>
</html> 