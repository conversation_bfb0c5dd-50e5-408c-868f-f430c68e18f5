<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار جميع الدول - الساعات والمناطق الزمنية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: center;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .clock-display {
            font-size: 2.5em;
            margin: 20px 0;
            padding: 20px;
            background: #333;
            color: #40E0D0;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
        }
        
        .country-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .country-card {
            background: #f8f9fa;
            border: 2px solid #40E0D0;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .country-card:hover {
            background: #e8f4f8;
            transform: translateY(-2px);
        }
        
        .country-card.active {
            background: #40E0D0;
            color: white;
        }
        
        .country-name {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .city-list {
            font-size: 0.9em;
            text-align: right;
        }
        
        .timezone-info {
            font-size: 0.8em;
            color: #666;
            margin-top: 5px;
        }
        
        .current-info {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: right;
        }
        
        .prayer-times {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: right;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار جميع الدول - الساعات والمناطق الزمنية</h1>
        
        <div class="current-info">
            <div><strong>الدولة الحالية:</strong> <span id="current-country">--</span></div>
            <div><strong>المدينة الحالية:</strong> <span id="current-city">--</span></div>
            <div><strong>المنطقة الزمنية:</strong> <span id="current-timezone">--</span></div>
        </div>
        
        <div class="clock-display" id="main-clock">
            --:--:--
        </div>
        
        <div class="status" id="status-display">
            جاري التحميل...
        </div>
        
        <h2>اختر الدولة والمدينة:</h2>
        
        <div class="country-grid" id="countries-grid">
            <!-- سيتم ملء الدول هنا بواسطة JavaScript -->
        </div>
        
        <div class="prayer-times" id="prayer-times-display" style="display: none;">
            <h3>مواقيت الصلاة:</h3>
            <div id="prayer-times-content"></div>
        </div>
    </div>

    <!-- تحميل المكتبات المطلوبة -->
    <script src="cities-database.js"></script>
    <script src="clock-new.js"></script>
    <script src="prayer-times-new.js"></script>
    
    <script>
        // قائمة الدول والمدن
        const countries = {
            "الأردن": {
                cities: ["عمان", "الزرقاء", "إربد", "العقبة"],
                timezone: "UTC+3",
                flag: "🇯🇴"
            },
            "السعودية": {
                cities: ["الرياض", "جدة", "مكة", "المدينة"],
                timezone: "UTC+3",
                flag: "🇸🇦"
            },
            "مصر": {
                cities: ["القاهرة", "الإسكندرية"],
                timezone: "UTC+2",
                flag: "🇪🇬"
            },
            "الإمارات": {
                cities: ["دبي", "أبوظبي"],
                timezone: "UTC+4",
                flag: "🇦🇪"
            },
            "قطر": {
                cities: ["الدوحة"],
                timezone: "UTC+3",
                flag: "🇶🇦"
            },
            "الكويت": {
                cities: ["مدينة الكويت"],
                timezone: "UTC+3",
                flag: "🇰🇼"
            },
            "عُمان": {
                cities: ["مسقط"],
                timezone: "UTC+4",
                flag: "🇴🇲"
            },
            "البحرين": {
                cities: ["المنامة"],
                timezone: "UTC+3",
                flag: "🇧🇭"
            },
            "لبنان": {
                cities: ["بيروت"],
                timezone: "UTC+3",
                flag: "🇱🇧"
            },
            "فلسطين": {
                cities: ["القدس"],
                timezone: "UTC+3",
                flag: "🇵🇸"
            },
            "العراق": {
                cities: ["بغداد"],
                timezone: "UTC+3",
                flag: "🇮🇶"
            },
            "سوريا": {
                cities: ["دمشق"],
                timezone: "UTC+3",
                flag: "🇸🇾"
            },
            "تركيا": {
                cities: ["إسطنبول"],
                timezone: "UTC+3",
                flag: "🇹🇷"
            }
        };
        
        let clockInterval;
        let currentCountry = null;
        let currentCity = null;
        
        // خريطة تحويل أسماء المدن إلى مفاتيح المناطق الزمنية
        const cityMapping = {
            "الأردن": {
                "عمان": "Asia/Amman",
                "الزرقاء": "Zarqa",
                "إربد": "Irbid",
                "العقبة": "Aqaba"
            },
            "السعودية": {
                "الرياض": "Asia/Riyadh",
                "جدة": "Jeddah",
                "مكة": "Asia/Makkah",
                "المدينة": "Asia/Madinah"
            },
            "مصر": {
                "القاهرة": "Africa/Cairo",
                "الإسكندرية": "Alexandria"
            },
            "الإمارات": {
                "دبي": "Asia/Dubai",
                "أبوظبي": "Abu_Dhabi"
            },
            "قطر": {
                "الدوحة": "Asia/Qatar"
            },
            "الكويت": {
                "مدينة الكويت": "Asia/Kuwait"
            },
            "عُمان": {
                "مسقط": "Asia/Muscat"
            },
            "البحرين": {
                "المنامة": "Asia/Bahrain"
            },
            "لبنان": {
                "بيروت": "Asia/Beirut"
            },
            "فلسطين": {
                "القدس": "Asia/Jerusalem"
            },
            "العراق": {
                "بغداد": "Asia/Baghdad"
            },
            "سوريا": {
                "دمشق": "Asia/Damascus"
            },
            "تركيا": {
                "إسطنبول": "Asia/Istanbul"
            }
        };
        
        function updateClock() {
            try {
                if (typeof getCurrentTimeForSelectedCity === 'function') {
                    const cityTime = getCurrentTimeForSelectedCity();
                    const timeString = cityTime.toLocaleTimeString('ar-SA');
                    document.getElementById('main-clock').textContent = timeString;
                } else {
                    document.getElementById('main-clock').textContent = 'خطأ في الدالة';
                }
            } catch (error) {
                console.error('خطأ في تحديث الساعة:', error);
            }
        }
        
        function updatePrayerTimesDisplay() {
            try {
                if (typeof getPrayerTimes === 'function') {
                    const times = getPrayerTimes();
                    if (times) {
                        const content = `
                            <div>الفجر: ${times.fajr}</div>
                            <div>الشروق: ${times.sunrise}</div>
                            <div>الظهر: ${times.dhuhr}</div>
                            <div>العصر: ${times.asr}</div>
                            <div>المغرب: ${times.maghrib}</div>
                            <div>العشاء: ${times.isha}</div>
                        `;
                        document.getElementById('prayer-times-content').innerHTML = content;
                        document.getElementById('prayer-times-display').style.display = 'block';
                    }
                }
            } catch (error) {
                console.error('خطأ في تحديث مواقيت الصلاة:', error);
            }
        }
        
        function selectCity(country, city) {
            console.log(`🔄 اختيار المدينة: ${city} في ${country}`);
            
            // الحصول على مفتاح المنطقة الزمنية
            const cityKey = cityMapping[country] && cityMapping[country][city] ? 
                           cityMapping[country][city] : 'Asia/Amman';
            
            // حفظ المدينة الجديدة
            localStorage.setItem('selectedCity', cityKey);
            
            // تحديث المعلومات الحالية
            currentCountry = country;
            currentCity = city;
            
            document.getElementById('current-country').textContent = country;
            document.getElementById('current-city').textContent = city;
            document.getElementById('current-timezone').textContent = cityKey;
            
            // تحديث الساعة ومواقيت الصلاة
            updateClock();
            updatePrayerTimesDisplay();
            
            // تحديث حالة البطاقات
            updateActiveCard(country);
            
            document.getElementById('status-display').className = 'status success';
            document.getElementById('status-display').textContent = `تم اختيار ${city}, ${country} بنجاح`;
            
            console.log(`✅ تم اختيار ${city} بنجاح`);
        }
        
        function updateActiveCard(activeCountry) {
            const cards = document.querySelectorAll('.country-card');
            cards.forEach(card => {
                if (card.dataset.country === activeCountry) {
                    card.classList.add('active');
                } else {
                    card.classList.remove('active');
                }
            });
        }
        
        function createCountryCards() {
            const grid = document.getElementById('countries-grid');
            
            Object.keys(countries).forEach(country => {
                const countryData = countries[country];
                
                const card = document.createElement('div');
                card.className = 'country-card';
                card.dataset.country = country;
                
                const citiesHtml = countryData.cities.map(city => 
                    `<div onclick="selectCity('${country}', '${city}')" style="cursor: pointer; padding: 5px; margin: 2px 0; background: rgba(255,255,255,0.5); border-radius: 3px;">${city}</div>`
                ).join('');
                
                card.innerHTML = `
                    <div class="country-name">${countryData.flag} ${country}</div>
                    <div class="timezone-info">${countryData.timezone}</div>
                    <div class="city-list">${citiesHtml}</div>
                `;
                
                grid.appendChild(card);
            });
        }
        
        function startTest() {
            console.log('🚀 بدء اختبار جميع الدول...');
            
            // إنشاء بطاقات الدول
            createCountryCards();
            
            // تحديث الساعة كل ثانية
            if (clockInterval) {
                clearInterval(clockInterval);
            }
            clockInterval = setInterval(updateClock, 1000);
            
            // اختيار عمان كمدينة افتراضية
            selectCity('الأردن', 'عمان');
            
            document.getElementById('status-display').className = 'status info';
            document.getElementById('status-display').textContent = 'اختبار جميع الدول جاهز - اختر أي مدينة لرؤية التغيير';
            
            console.log('✅ تم بدء الاختبار بنجاح');
        }
        
        // بدء الاختبار عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(startTest, 1000);
        });
        
        // فحص الدوال المطلوبة
        setTimeout(() => {
            console.log('🔍 فحص الدوال المطلوبة:');
            console.log('getCurrentTimeForSelectedCity:', typeof getCurrentTimeForSelectedCity);
            console.log('getPrayerTimes:', typeof getPrayerTimes);
            console.log('CITIES_DATABASE:', typeof CITIES_DATABASE);
            
            if (typeof getCurrentTimeForSelectedCity !== 'function') {
                document.getElementById('status-display').className = 'status error';
                document.getElementById('status-display').textContent = 'خطأ: بعض الدوال المطلوبة غير متوفرة';
            }
        }, 2000);
    </script>
</body>
</html>
