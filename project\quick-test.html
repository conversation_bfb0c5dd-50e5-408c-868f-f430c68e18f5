<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع للتطبيق</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .info {
            border-left-color: #17a2b8;
            background: #d1ecf1;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>اختبار سريع للتطبيق الرسمي</h1>
    
    <div class="test-section">
        <h3>1. اختبار المكتبات:</h3>
        <div id="library-status"></div>
    </div>
    
    <div class="test-section">
        <h3>2. اختبار حساب مواقيت الصلاة:</h3>
        <button onclick="testPrayerCalculation()">اختبار الحساب</button>
        <div id="prayer-result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. اختبار الساعة:</h3>
        <div id="clock-display" style="font-size: 2em; text-align: center; margin: 20px;"></div>
    </div>
    
    <div class="test-section">
        <h3>4. اختبار التطبيق الرسمي:</h3>
        <button onclick="openMainApp()">فتح التطبيق الرسمي</button>
        <div id="app-status"></div>
    </div>

    <!-- تحميل المكتبات الأساسية -->
    <script src="js/PrayTimes.js"></script>

    <script>
        // اختبار المكتبات
        function testLibraries() {
            const statusDiv = document.getElementById('library-status');
            let html = '';
            
            // فحص PrayTimes
            if (typeof PrayTimes !== 'undefined') {
                html += '<div class="status success">✅ مكتبة PrayTimes متوفرة</div>';
            } else {
                html += '<div class="status error">❌ مكتبة PrayTimes غير متوفرة</div>';
            }
            
            // فحص localStorage
            if (typeof localStorage !== 'undefined') {
                html += '<div class="status success">✅ localStorage متوفر</div>';
            } else {
                html += '<div class="status error">❌ localStorage غير متوفر</div>';
            }
            
            statusDiv.innerHTML = html;
        }
        
        // اختبار حساب مواقيت الصلاة
        function testPrayerCalculation() {
            const resultDiv = document.getElementById('prayer-result');
            
            try {
                if (typeof PrayTimes === 'undefined') {
                    resultDiv.innerHTML = '<div class="status error">❌ مكتبة PrayTimes غير متوفرة</div>';
                    return;
                }
                
                const pt = new PrayTimes('MWL');
                const coordinates = [31.9539, 35.9106]; // عمان
                const date = new Date();
                const times = pt.getTimes(date, coordinates, 3);
                
                let timesHtml = '<div class="status success"><strong>مواقيت الصلاة لعمان:</strong><br>';
                for (let prayer in times) {
                    if (times[prayer] !== '-----') {
                        timesHtml += `- ${prayer}: ${times[prayer]}<br>`;
                    }
                }
                timesHtml += '</div>';
                
                resultDiv.innerHTML = timesHtml;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="status error">❌ خطأ: ${error.message}</div>`;
            }
        }
        
        // تحديث الساعة
        function updateClock() {
            const clockDiv = document.getElementById('clock-display');
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            const dateString = now.toLocaleDateString('ar-SA');
            clockDiv.innerHTML = `${timeString}<br><small>${dateString}</small>`;
        }
        
        // فتح التطبيق الرسمي
        function openMainApp() {
            const statusDiv = document.getElementById('app-status');
            statusDiv.innerHTML = '<div class="status info">جاري فتح التطبيق الرسمي...</div>';
            
            try {
                window.open('index.html', '_blank');
                statusDiv.innerHTML = '<div class="status success">✅ تم فتح التطبيق الرسمي</div>';
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ خطأ في فتح التطبيق: ${error.message}</div>`;
            }
        }
        
        // تشغيل الاختبارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('بدء الاختبار السريع...');
            testLibraries();
            testPrayerCalculation();
            
            // تحديث الساعة كل ثانية
            updateClock();
            setInterval(updateClock, 1000);
        });
    </script>
</body>
</html> 