<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع - تحديث الساعات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: white;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .clock-display {
            text-align: center;
            margin: 20px 0;
        }
        
        .analog-clock {
            width: 150px;
            height: 150px;
            border: 3px solid #fff;
            border-radius: 50%;
            margin: 20px auto;
            position: relative;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .hour-hand, .minute-hand, .second-hand {
            position: absolute;
            top: 50%;
            left: 50%;
            transform-origin: 50% 0;
            background: #fff;
        }
        
        .hour-hand {
            width: 3px;
            height: 45px;
            margin-left: -1.5px;
            margin-top: -45px;
        }
        
        .minute-hand {
            width: 2px;
            height: 60px;
            margin-left: -1px;
            margin-top: -60px;
        }
        
        .second-hand {
            width: 1px;
            height: 70px;
            margin-left: -0.5px;
            margin-top: -70px;
            background: #ff6b6b;
        }
        
        .digital-clock {
            font-size: 1.5em;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .control-group {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
        }
        
        select, button {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: none;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 14px;
        }
        
        select option {
            background: #333;
            color: white;
        }
        
        button {
            background: #4CAF50;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 14px;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .info { color: #2196F3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار سريع - تحديث الساعات في التطبيق الرئيسي</h1>
        
        <div class="test-section">
            <h2>الساعات الحالية</h2>
            <div class="clock-display">
                <div class="analog-clock">
                    <div class="hour-hand"></div>
                    <div class="minute-hand"></div>
                    <div class="second-hand"></div>
                </div>
                <div class="digital-clock" id="digital-clock">00:00:00</div>
                <div class="status" id="status">حالة الساعة: جاري التحميل...</div>
            </div>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <h3>اختيار المدينة</h3>
                <select id="city-select">
                    <option value="Asia/Amman">عمان - الأردن</option>
                    <option value="Asia/Riyadh">الرياض - السعودية</option>
                    <option value="Asia/Dubai">دبي - الإمارات</option>
                    <option value="Asia/Cairo">القاهرة - مصر</option>
                    <option value="Asia/Beirut">بيروت - لبنان</option>
                </select>
            </div>
            
            <div class="control-group">
                <h3>طريقة الحساب</h3>
                <select id="method-select">
                    <option value="MWL">MWL - Muslim World League</option>
                    <option value="ISNA">ISNA - Islamic Society of North America</option>
                    <option value="Egypt">Egypt - Egyptian General Authority</option>
                    <option value="Makkah">Makkah - Umm Al-Qura University</option>
                    <option value="Karachi">Karachi - University of Islamic Sciences</option>
                </select>
            </div>
        </div>
        
        <div class="test-section">
            <h3>اختبار التحديث</h3>
            <button onclick="testUpdate()">اختبار تحديث الساعات</button>
            <button onclick="openMainApp()">فتح التطبيق الرئيسي</button>
        </div>
        
        <div class="test-section">
            <h3>تعليمات الاختبار</h3>
            <div class="status">
                <p><strong>لاختبار التحديث في التطبيق الرئيسي:</strong></p>
                <ol>
                    <li>اضغط على "فتح التطبيق الرئيسي"</li>
                    <li>افتح وحدة التحكم (F12)</li>
                    <li>غيّر المدينة أو طريقة الحساب</li>
                    <li>راقب رسائل التأكيد في وحدة التحكم</li>
                    <li>تأكد من تحديث الساعات تلقائياً</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // دوال الساعة
        function updateAnalogClock() {
            const now = new Date();
            const hours = now.getHours() % 12;
            const minutes = now.getMinutes();
            const seconds = now.getSeconds();
            
            const hourHand = document.querySelector('.hour-hand');
            const minuteHand = document.querySelector('.minute-hand');
            const secondHand = document.querySelector('.second-hand');
            
            if (hourHand && minuteHand && secondHand) {
                const hourDeg = (hours * 30) + (minutes * 0.5);
                const minuteDeg = (minutes * 6) + (seconds * 0.1);
                const secondDeg = seconds * 6;
                
                hourHand.style.transform = `rotate(${hourDeg}deg)`;
                minuteHand.style.transform = `rotate(${minuteDeg}deg)`;
                secondHand.style.transform = `rotate(${secondDeg}deg)`;
            }
        }

        function updateDigitalClock() {
            const now = new Date();
            const hours = now.getHours();
            const minutes = now.getMinutes();
            const seconds = now.getSeconds();
            
            const timeString = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
            
            const digitalClock = document.getElementById('digital-clock');
            if (digitalClock) {
                digitalClock.textContent = timeString;
            }
        }

        function updateStatus(message) {
            const status = document.getElementById('status');
            if (status) {
                status.textContent = `حالة الساعة: ${message}`;
            }
        }

        function testUpdate() {
            updateStatus('جاري اختبار التحديث...');
            
            setTimeout(() => {
                updateAnalogClock();
                updateDigitalClock();
                updateStatus('تم اختبار التحديث بنجاح');
            }, 500);
        }

        function openMainApp() {
            window.open('index.html', '_blank');
        }

        // مراقبة التغييرات
        function setupChangeMonitoring() {
            const citySelect = document.getElementById('city-select');
            const methodSelect = document.getElementById('method-select');
            
            if (citySelect) {
                citySelect.addEventListener('change', function() {
                    updateStatus(`تم تغيير المدينة إلى: ${this.value}`);
                    setTimeout(() => {
                        updateAnalogClock();
                        updateDigitalClock();
                    }, 100);
                });
            }
            
            if (methodSelect) {
                methodSelect.addEventListener('change', function() {
                    updateStatus(`تم تغيير طريقة الحساب إلى: ${this.value}`);
                    setTimeout(() => {
                        updateAnalogClock();
                        updateDigitalClock();
                    }, 100);
                });
            }
        }

        // بدء تشغيل النظام
        function startSystem() {
            updateStatus('تم بدء تشغيل النظام');
            
            // تحديث أولي
            updateAnalogClock();
            updateDigitalClock();
            
            // بدء التحديث المستمر
            setInterval(updateAnalogClock, 1000);
            setInterval(updateDigitalClock, 1000);
            
            // إعداد مراقبة التغييرات
            setupChangeMonitoring();
        }

        // بدء التشغيل عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(startSystem, 500);
        });
    </script>
</body>
</html> 