<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار دقة الأوقات ومواقيت الصلاة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: center;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .time-comparison {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .city-card {
            background: #f8f9fa;
            border: 2px solid #40E0D0;
            border-radius: 10px;
            padding: 15px;
            text-align: right;
        }
        
        .city-name {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #40E0D0;
        }
        
        .time-info {
            margin: 5px 0;
            padding: 5px;
            background: #e8f4f8;
            border-radius: 5px;
        }
        
        .prayer-times {
            background: #f0f8ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .real-time {
            font-size: 1.5em;
            font-weight: bold;
            color: #2e7d32;
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .controls {
            margin: 20px 0;
            padding: 15px;
            background: #e8f4f8;
            border-radius: 8px;
        }
        
        .btn {
            background: #40E0D0;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        
        .btn:hover {
            background: #369fb8;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار دقة الأوقات ومواقيت الصلاة</h1>
        
        <div class="controls">
            <h3>التحكم في الاختبار:</h3>
            <button class="btn" onclick="startAccuracyTest()">بدء اختبار الدقة</button>
            <button class="btn" onclick="compareWithRealTime()">مقارنة مع الوقت الحقيقي</button>
            <button class="btn" onclick="testAllCities()">اختبار جميع المدن</button>
            <button class="btn" onclick="validatePrayerTimes()">التحقق من مواقيت الصلاة</button>
        </div>
        
        <div class="status" id="test-status">
            جاري التحضير للاختبار...
        </div>
        
        <div class="time-comparison" id="comparison-grid">
            <!-- سيتم ملء النتائج هنا -->
        </div>
    </div>

    <!-- تحميل المكتبات المطلوبة -->
    <script src="cities-database.js"></script>
    <script src="clock-new.js"></script>
    <script src="prayer-times-new.js"></script>
    
    <script>
        // قائمة المدن للاختبار
        const testCities = [
            { key: 'Asia/Amman', name: 'عمان', country: 'الأردن', expectedUTC: '+3' },
            { key: 'Asia/Riyadh', name: 'الرياض', country: 'السعودية', expectedUTC: '+3' },
            { key: 'Asia/Dubai', name: 'دبي', country: 'الإمارات', expectedUTC: '+4' },
            { key: 'Africa/Cairo', name: 'القاهرة', country: 'مصر', expectedUTC: '+2' },
            { key: 'Asia/Kuwait', name: 'الكويت', country: 'الكويت', expectedUTC: '+3' },
            { key: 'Asia/Qatar', name: 'الدوحة', country: 'قطر', expectedUTC: '+3' },
            { key: 'Asia/Beirut', name: 'بيروت', country: 'لبنان', expectedUTC: '+3' },
            { key: 'Asia/Baghdad', name: 'بغداد', country: 'العراق', expectedUTC: '+3' }
        ];
        
        function startAccuracyTest() {
            console.log('🚀 بدء اختبار دقة الأوقات...');
            
            const status = document.getElementById('test-status');
            status.className = 'status info';
            status.textContent = 'جاري اختبار دقة الأوقات...';
            
            const grid = document.getElementById('comparison-grid');
            grid.innerHTML = '';
            
            testCities.forEach(city => {
                const card = createCityTestCard(city);
                grid.appendChild(card);
            });
            
            status.className = 'status success';
            status.textContent = 'تم اختبار جميع المدن بنجاح';
        }
        
        function createCityTestCard(city) {
            const card = document.createElement('div');
            card.className = 'city-card';
            
            // حفظ المدينة الحالية واختبارها
            const originalCity = localStorage.getItem('selectedCity');
            localStorage.setItem('selectedCity', city.key);
            
            // الحصول على الوقت من النظام
            let systemTime = '--:--:--';
            let realTime = '--:--:--';
            let timeDifference = 'غير محدد';
            
            try {
                if (typeof getCurrentTimeForSelectedCity === 'function') {
                    const cityTime = getCurrentTimeForSelectedCity();
                    systemTime = cityTime.toLocaleTimeString('ar-SA');
                }
                
                // الحصول على الوقت الحقيقي للمدينة
                const now = new Date();
                const actualCityTime = new Date(now.toLocaleString("en-US", {timeZone: city.key}));
                realTime = actualCityTime.toLocaleTimeString('ar-SA');
                
                // حساب الفرق
                const utcOffset = (actualCityTime.getTime() - new Date(now.toUTCString()).getTime()) / (1000 * 60 * 60);
                timeDifference = `UTC${utcOffset >= 0 ? '+' : ''}${utcOffset}`;
                
            } catch (error) {
                console.error('خطأ في اختبار المدينة:', city.name, error);
            }
            
            // الحصول على مواقيت الصلاة
            let prayerTimes = null;
            try {
                if (typeof getPrayerTimes === 'function') {
                    prayerTimes = getPrayerTimes();
                }
            } catch (error) {
                console.error('خطأ في الحصول على مواقيت الصلاة:', error);
            }
            
            // إنشاء محتوى البطاقة
            let prayerTimesHtml = '';
            if (prayerTimes) {
                prayerTimesHtml = `
                    <div class="prayer-times">
                        <strong>مواقيت الصلاة:</strong><br>
                        الفجر: ${prayerTimes.fajr} | الظهر: ${prayerTimes.dhuhr}<br>
                        العصر: ${prayerTimes.asr} | المغرب: ${prayerTimes.maghrib}<br>
                        العشاء: ${prayerTimes.isha}
                    </div>
                `;
            }
            
            card.innerHTML = `
                <div class="city-name">${city.name}, ${city.country}</div>
                <div class="time-info">المنطقة الزمنية: ${city.key}</div>
                <div class="time-info">التوقيت المتوقع: ${city.expectedUTC}</div>
                <div class="time-info">التوقيت الفعلي: ${timeDifference}</div>
                <div class="real-time">الوقت الحقيقي: ${realTime}</div>
                <div class="time-info">وقت النظام: ${systemTime}</div>
                <div class="time-info">حالة التطابق: ${systemTime === realTime ? '✅ متطابق' : '⚠️ مختلف'}</div>
                ${prayerTimesHtml}
            `;
            
            // استعادة المدينة الأصلية
            if (originalCity) {
                localStorage.setItem('selectedCity', originalCity);
            }
            
            return card;
        }
        
        function compareWithRealTime() {
            console.log('🔍 مقارنة مع الوقت الحقيقي...');
            
            const status = document.getElementById('test-status');
            status.className = 'status info';
            status.textContent = 'جاري المقارنة مع الأوقات الحقيقية...';
            
            let results = '<h3>نتائج المقارنة:</h3>';
            let accurateCount = 0;
            
            testCities.forEach(city => {
                try {
                    const now = new Date();
                    const realTime = new Date(now.toLocaleString("en-US", {timeZone: city.key}));
                    
                    localStorage.setItem('selectedCity', city.key);
                    const systemTime = getCurrentTimeForSelectedCity();
                    
                    const timeDiff = Math.abs(realTime.getTime() - systemTime.getTime());
                    const isAccurate = timeDiff < 60000; // أقل من دقيقة واحدة
                    
                    if (isAccurate) accurateCount++;
                    
                    results += `
                        <div style="padding: 5px; margin: 5px; background: ${isAccurate ? '#d4edda' : '#f8d7da'}; border-radius: 5px;">
                            ${city.name}: ${isAccurate ? '✅ دقيق' : '❌ غير دقيق'} 
                            (فرق: ${Math.round(timeDiff/1000)} ثانية)
                        </div>
                    `;
                } catch (error) {
                    results += `<div style="padding: 5px; margin: 5px; background: #f8d7da; border-radius: 5px;">
                        ${city.name}: ❌ خطأ في الاختبار
                    </div>`;
                }
            });
            
            results += `<h4>النتيجة النهائية: ${accurateCount}/${testCities.length} مدن دقيقة</h4>`;
            
            document.getElementById('comparison-grid').innerHTML = results;
            
            status.className = accurateCount === testCities.length ? 'status success' : 'status warning';
            status.textContent = `تم الانتهاء من المقارنة - ${accurateCount}/${testCities.length} مدن دقيقة`;
        }
        
        function testAllCities() {
            console.log('🌍 اختبار جميع المدن...');
            startAccuracyTest();
        }
        
        function validatePrayerTimes() {
            console.log('🕌 التحقق من مواقيت الصلاة...');
            
            const status = document.getElementById('test-status');
            status.className = 'status info';
            status.textContent = 'جاري التحقق من مواقيت الصلاة...';
            
            let results = '<h3>التحقق من مواقيت الصلاة:</h3>';
            let validCount = 0;
            
            testCities.forEach(city => {
                try {
                    localStorage.setItem('selectedCity', city.key);
                    const times = getPrayerTimes();
                    
                    const isValid = times && times.fajr && times.dhuhr && times.maghrib && times.isha;
                    if (isValid) validCount++;
                    
                    results += `
                        <div style="padding: 10px; margin: 5px; background: ${isValid ? '#d4edda' : '#f8d7da'}; border-radius: 5px; text-align: right;">
                            <strong>${city.name}:</strong> ${isValid ? '✅ مواقيت متوفرة' : '❌ مواقيت مفقودة'}<br>
                            ${isValid ? `الفجر: ${times.fajr} | الظهر: ${times.dhuhr} | المغرب: ${times.maghrib} | العشاء: ${times.isha}` : 'لا توجد مواقيت'}
                        </div>
                    `;
                } catch (error) {
                    results += `<div style="padding: 10px; margin: 5px; background: #f8d7da; border-radius: 5px;">
                        ${city.name}: ❌ خطأ في الحصول على المواقيت
                    </div>`;
                }
            });
            
            results += `<h4>النتيجة: ${validCount}/${testCities.length} مدن لها مواقيت صحيحة</h4>`;
            
            document.getElementById('comparison-grid').innerHTML = results;
            
            status.className = validCount === testCities.length ? 'status success' : 'status warning';
            status.textContent = `تم التحقق من المواقيت - ${validCount}/${testCities.length} مدن صحيحة`;
        }
        
        // بدء الاختبار عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const status = document.getElementById('test-status');
                status.className = 'status success';
                status.textContent = 'جاهز للاختبار - اختر نوع الاختبار من الأزرار أعلاه';
            }, 1000);
        });
    </script>
</body>
</html>
