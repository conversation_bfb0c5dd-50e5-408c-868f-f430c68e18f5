/**
 * ملف مواقيت الصلاة الجديد
 * يحتوي على جميع الدوال المتعلقة بمواقيت الصلاة
 */

// تعريف مواقيت الصلاة الثابتة لجميع المدن
const PRAYER_TIMES_DATA = {
    // طريقة رابطة العالم الإسلامي (MWL)
    'MWL': {
        'Asia/Amman': {
            fajr: '04:25',
            sunrise: '05:55',
            dhuhr: '12:35',
            asr: '16:05',
            maghrib: '19:15',
            isha: '20:45'
        },
        'Asia/Riyadh': {
            fajr: '04:10',
            sunrise: '05:40',
            dhuhr: '12:20',
            asr: '15:50',
            maghrib: '19:00',
            isha: '20:30'
        },
        'Asia/Dubai': {
            fajr: '04:35',
            sunrise: '06:05',
            dhuhr: '12:50',
            asr: '16:20',
            maghrib: '19:35',
            isha: '21:05'
        },
        'Africa/Cairo': {
            fajr: '04:15',
            sunrise: '05:45',
            dhuhr: '12:30',
            asr: '16:00',
            maghrib: '19:15',
            isha: '20:45'
        },
        'Asia/Jerusalem': {
            fajr: '04:20',
            sunrise: '05:50',
            dhuhr: '12:35',
            asr: '16:05',
            maghrib: '19:20',
            isha: '20:50'
        },
        'Asia/Makkah': {
            fajr: '04:40',
            sunrise: '06:10',
            dhuhr: '12:20',
            asr: '15:50',
            maghrib: '18:30',
            isha: '20:00'
        },
        'Asia/Madinah': {
            fajr: '04:35',
            sunrise: '06:05',
            dhuhr: '12:15',
            asr: '15:45',
            maghrib: '18:25',
            isha: '19:55'
        },
        'Asia/Istanbul': {
            fajr: '04:05',
            sunrise: '05:35',
            dhuhr: '13:00',
            asr: '16:30',
            maghrib: '20:25',
            isha: '21:55'
        },
        'Asia/Baghdad': {
            fajr: '04:05',
            sunrise: '05:35',
            dhuhr: '12:20',
            asr: '15:50',
            maghrib: '19:05',
            isha: '20:35'
        },
        'Asia/Kuwait': {
            fajr: '04:05',
            sunrise: '05:35',
            dhuhr: '12:05',
            asr: '15:35',
            maghrib: '18:35',
            isha: '20:05'
        },
        'Asia/Qatar': {
            fajr: '04:00',
            sunrise: '05:30',
            dhuhr: '12:00',
            asr: '15:30',
            maghrib: '18:30',
            isha: '20:00'
        },
        'Asia/Bahrain': {
            fajr: '04:05',
            sunrise: '05:35',
            dhuhr: '12:05',
            asr: '15:35',
            maghrib: '18:35',
            isha: '20:05'
        },
        'Asia/Beirut': {
            fajr: '04:20',
            sunrise: '05:50',
            dhuhr: '12:35',
            asr: '16:05',
            maghrib: '19:20',
            isha: '20:50'
        },
        'Asia/Damascus': {
            fajr: '04:15',
            sunrise: '05:45',
            dhuhr: '12:30',
            asr: '16:00',
            maghrib: '19:15',
            isha: '20:45'
        },
        'Zarqa': {
            fajr: '04:15',
            sunrise: '05:45',
            dhuhr: '12:30',
            asr: '16:00',
            maghrib: '19:15',
            isha: '20:45'
        },
        'Irbid': {
            fajr: '04:15',
            sunrise: '05:45',
            dhuhr: '12:30',
            asr: '16:00',
            maghrib: '19:15',
            isha: '20:45'
        },
        'Aqaba': {
            fajr: '04:15',
            sunrise: '05:45',
            dhuhr: '12:30',
            asr: '16:00',
            maghrib: '19:15',
            isha: '20:45'
        },
        'Jeddah': {
            fajr: '04:40',
            sunrise: '06:10',
            dhuhr: '12:20',
            asr: '15:50',
            maghrib: '18:30',
            isha: '20:00'
        },
        'Dammam': {
            fajr: '04:00',
            sunrise: '05:30',
            dhuhr: '12:15',
            asr: '15:45',
            maghrib: '19:00',
            isha: '20:30'
        },
        'Abu_Dhabi': {
            fajr: '04:30',
            sunrise: '06:00',
            dhuhr: '12:45',
            asr: '16:15',
            maghrib: '19:30',
            isha: '21:00'
        },
        'Alexandria': {
            fajr: '04:10',
            sunrise: '05:40',
            dhuhr: '12:25',
            asr: '15:55',
            maghrib: '19:10',
            isha: '20:40'
        },
        // المدن المفقودة
        'Asia/Istanbul': {
            fajr: '04:05',
            sunrise: '05:35',
            dhuhr: '13:00',
            asr: '16:30',
            maghrib: '20:25',
            isha: '21:55'
        },
        'Asia/Muscat': {
            fajr: '04:25',
            sunrise: '05:55',
            dhuhr: '12:25',
            asr: '15:55',
            maghrib: '18:55',
            isha: '20:25'
        },
        'Asia/Beirut': {
            fajr: '04:15',
            sunrise: '05:45',
            dhuhr: '12:30',
            asr: '16:00',
            maghrib: '19:15',
            isha: '20:45'
        },
        'Africa/Tunis': {
            fajr: '04:05',
            sunrise: '05:35',
            dhuhr: '12:45',
            asr: '16:15',
            maghrib: '19:55',
            isha: '21:25'
        },
        'Africa/Algiers': {
            fajr: '04:10',
            sunrise: '05:40',
            dhuhr: '12:50',
            asr: '16:20',
            maghrib: '20:00',
            isha: '21:30'
        },
        'Africa/Casablanca': {
            fajr: '04:30',
            sunrise: '06:00',
            dhuhr: '13:10',
            asr: '16:40',
            maghrib: '20:20',
            isha: '21:50'
        },
        'Asia/Tehran': {
            fajr: '04:35',
            sunrise: '06:05',
            dhuhr: '13:05',
            asr: '16:35',
            maghrib: '20:05',
            isha: '21:35'
        },
        'Asia/Karachi': {
            fajr: '04:45',
            sunrise: '06:15',
            dhuhr: '12:15',
            asr: '15:45',
            maghrib: '18:15',
            isha: '19:45'
        },
        'Asia/Kuala_Lumpur': {
            fajr: '05:50',
            sunrise: '07:20',
            dhuhr: '13:20',
            asr: '16:50',
            maghrib: '19:20',
            isha: '20:50'
        },
        'Asia/Jakarta': {
            fajr: '04:40',
            sunrise: '06:10',
            dhuhr: '12:10',
            asr: '15:40',
            maghrib: '18:10',
            isha: '19:40'
        }
    },
    
    // طريقة الجمعية الإسلامية لأمريكا الشمالية (ISNA)
    'ISNA': {
        'Asia/Amman': {
            fajr: '04:30',
            sunrise: '05:45',
            dhuhr: '12:30',
            asr: '16:00',
            maghrib: '19:15',
            isha: '20:30'
        },
        'Asia/Riyadh': {
            fajr: '04:15',
            sunrise: '05:30',
            dhuhr: '12:15',
            asr: '15:45',
            maghrib: '19:00',
            isha: '20:15'
        }
        // يمكن إضافة المزيد من المدن هنا
    },
    
    // طريقة جامعة أم القرى، مكة المكرمة (Makkah)
    'Makkah': {
        'Asia/Amman': {
            fajr: '04:20',
            sunrise: '05:45',
            dhuhr: '12:30',
            asr: '16:00',
            maghrib: '19:15',
            isha: '21:00'
        },
        'Asia/Riyadh': {
            fajr: '04:05',
            sunrise: '05:30',
            dhuhr: '12:15',
            asr: '15:45',
            maghrib: '19:00',
            isha: '20:45'
        }
        // يمكن إضافة المزيد من المدن هنا
    },
    
    // طريقة مصر (Egypt)
    'Egypt': {
        'Asia/Amman': {
            fajr: '04:10',
            sunrise: '05:45',
            dhuhr: '12:30',
            asr: '16:00',
            maghrib: '19:15',
            isha: '20:50'
        },
        'Asia/Riyadh': {
            fajr: '03:55',
            sunrise: '05:30',
            dhuhr: '12:15',
            asr: '15:45',
            maghrib: '19:00',
            isha: '20:35'
        }
        // يمكن إضافة المزيد من المدن هنا
    }
};

// تعريف متغير عالمي لمواقيت الصلاة
window.prayerTimes = {};

// دالة للحصول على مواقيت الصلاة المحسوبة بدقة
function getPrayerTimes() {
    try {
        // الحصول على المدينة الحالية
        const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
        console.log('حساب مواقيت الصلاة للمدينة:', currentCity);

        // الحصول على طريقة الحساب المحفوظة
        const savedMethod = localStorage.getItem('calculationMethod') || 'MWL';

        // محاولة الحصول على الإحداثيات من قاعدة البيانات
        let coordinates = null;
        if (typeof CITIES_DATABASE !== 'undefined' && CITIES_DATABASE[currentCity]) {
            coordinates = {
                latitude: CITIES_DATABASE[currentCity].latitude,
                longitude: CITIES_DATABASE[currentCity].longitude
            };
        }

        // إذا كانت مكتبة PrayTimes متوفرة والإحداثيات موجودة، احسب المواقيت
        if (typeof PrayTimes !== 'undefined' && coordinates) {
            console.log('حساب مواقيت دقيقة للإحداثيات:', coordinates);

            const prayTimes = new PrayTimes(savedMethod);
            const today = new Date();

            // حساب المواقيت للتاريخ الحالي
            const calculatedTimes = prayTimes.getTimes(today, [coordinates.latitude, coordinates.longitude], getTimezoneOffset(currentCity));

            // تحويل المواقيت إلى تنسيق 24 ساعة
            const times = {
                fajr: formatTime(calculatedTimes.fajr),
                sunrise: formatTime(calculatedTimes.sunrise),
                dhuhr: formatTime(calculatedTimes.dhuhr),
                asr: formatTime(calculatedTimes.asr),
                maghrib: formatTime(calculatedTimes.maghrib),
                isha: formatTime(calculatedTimes.isha)
            };

            console.log('مواقيت محسوبة:', times);

            // تحديث المواقيت العالمية
            if (!window.prayerTimes) window.prayerTimes = {};
            window.prayerTimes[currentCity] = times;

            return times;
        }

        // إذا لم تكن المكتبة متوفرة، استخدم المواقيت المحفوظة
        console.log('استخدام المواقيت المحفوظة مسبقاً');
        return getPrayerTimesFromDatabase();

    } catch (error) {
        console.error('خطأ في حساب مواقيت الصلاة:', error);
        return getPrayerTimesFromDatabase();
    }
}

// دالة للحصول على المواقيت من قاعدة البيانات المحفوظة
function getPrayerTimesFromDatabase() {
    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    const savedMethod = localStorage.getItem('calculationMethod') || 'MWL';

    let times;

    // التحقق من وجود طريقة الحساب المحددة
    if (PRAYER_TIMES_DATA[savedMethod]) {
        // التحقق من وجود المدينة المحددة
        if (PRAYER_TIMES_DATA[savedMethod][currentCity]) {
            times = PRAYER_TIMES_DATA[savedMethod][currentCity];
        } else {
            // إذا لم تكن المدينة موجودة، استخدم عمان كاحتياطي
            times = PRAYER_TIMES_DATA[savedMethod]['Asia/Amman'];
        }
    } else {
        // إذا لم تكن طريقة الحساب موجودة، استخدم MWL كاحتياطي
        if (PRAYER_TIMES_DATA['MWL'][currentCity]) {
            times = PRAYER_TIMES_DATA['MWL'][currentCity];
        } else {
            times = PRAYER_TIMES_DATA['MWL']['Asia/Amman'];
        }
    }

    // تحديث المواقيت العالمية للمدينة المحددة
    if (!window.prayerTimes) window.prayerTimes = {};
    window.prayerTimes[currentCity] = times;

    return times;
}

// دالة للحصول على فرق التوقيت للمدينة
function getTimezoneOffset(cityKey) {
    try {
        const now = new Date();
        if (cityKey.includes('/')) {
            // استخدام المنطقة الزمنية لحساب الفرق
            const cityTime = new Date(now.toLocaleString("en-US", {timeZone: cityKey}));
            const utcTime = new Date(now.toUTCString());
            return (cityTime.getTime() - utcTime.getTime()) / (1000 * 60 * 60); // بالساعات
        }

        // قيم افتراضية للمدن
        const timezoneOffsets = {
            'Zarqa': 3, 'Irbid': 3, 'Aqaba': 3,
            'Jeddah': 3, 'Dammam': 3,
            'Abu_Dhabi': 4, 'Alexandria': 2
        };

        return timezoneOffsets[cityKey] || 3;
    } catch (error) {
        console.error('خطأ في حساب فرق التوقيت:', error);
        return 3; // قيمة افتراضية
    }
}

// دالة لتنسيق الوقت
function formatTime(timeString) {
    try {
        if (!timeString) return '00:00';

        // إذا كان الوقت بتنسيق عشري (مثل 5.75)
        if (typeof timeString === 'number') {
            const hours = Math.floor(timeString);
            const minutes = Math.round((timeString - hours) * 60);
            return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
        }

        // إذا كان الوقت بتنسيق نصي
        if (typeof timeString === 'string') {
            const parts = timeString.split(':');
            if (parts.length >= 2) {
                const hours = parseInt(parts[0]);
                const minutes = parseInt(parts[1]);
                return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
            }
        }

        return timeString;
    } catch (error) {
        console.error('خطأ في تنسيق الوقت:', error);
        return '00:00';
    }
}

// دالة لتحديث عرض مواقيت الصلاة
function updatePrayerTimesDisplay(times, format = '24') {
    if (!times) {
        console.warn('لا توجد مواقيت صلاة لعرضها');
        return;
    }

    console.log('تحديث عرض مواقيت الصلاة:', times);

    // قائمة بجميع العناصر المحتملة لمواقيت الصلاة
    const elements = [
        { id: 'fajr-time', time: times.fajr },
        { id: 'sunrise-time', time: times.sunrise },
        { id: 'dhuhr-time', time: times.dhuhr },
        { id: 'asr-time', time: times.asr },
        { id: 'maghrib-time', time: times.maghrib },
        { id: 'isha-time', time: times.isha },
        // عناصر إضافية قد تكون موجودة
        { id: 'fajr', time: times.fajr },
        { id: 'sunrise', time: times.sunrise },
        { id: 'dhuhr', time: times.dhuhr },
        { id: 'asr', time: times.asr },
        { id: 'maghrib', time: times.maghrib },
        { id: 'isha', time: times.isha }
    ];

    // تحديث جميع العناصر الموجودة
    elements.forEach(element => {
        const el = document.getElementById(element.id);
        if (el && element.time) {
            el.textContent = element.time;
            console.log(`تم تحديث ${element.id}: ${element.time}`);
        }
    });

    // تحديث العناصر بالكلاسات أيضاً
    const classElements = [
        { class: 'fajr-time', time: times.fajr },
        { class: 'dhuhr-time', time: times.dhuhr },
        { class: 'asr-time', time: times.asr },
        { class: 'maghrib-time', time: times.maghrib },
        { class: 'isha-time', time: times.isha }
    ];

    classElements.forEach(element => {
        const els = document.getElementsByClassName(element.class);
        for (let i = 0; i < els.length; i++) {
            if (element.time) {
                els[i].textContent = element.time;
            }
        }
    });

    console.log('تم تحديث عرض مواقيت الصلاة بنجاح');
}

// دالة لإعادة تحميل وتحديث مواقيت الصلاة مع التحديث المستمر
function reloadPrayerTimes() {
    console.log('🔄 إعادة تحميل مواقيت الصلاة...');

    try {
        // الحصول على مواقيت الصلاة الجديدة (محسوبة أو من قاعدة البيانات)
        const times = getPrayerTimes();

        if (times) {
            console.log('✅ تم الحصول على مواقيت الصلاة:', times);

            // تحديث العرض
            updatePrayerTimesDisplay(times);

            // تحديث المتغير العالمي
            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
            if (!window.prayerTimes) {
                window.prayerTimes = {};
            }
            window.prayerTimes[currentCity] = times;

            // تحديث مواقيت الصلاة في جميع أجزاء التطبيق
            if (typeof updatePrayerTimes === 'function') {
                setTimeout(() => {
                    updatePrayerTimes();
                    console.log('✅ تم تحديث عرض مواقيت الصلاة في التطبيق');
                }, 100);
            }

            // تحديث المستطيل الأفقي
            setTimeout(() => {
                updateHorizontalPrayerTimesDisplay(times);
            }, 200);

            console.log('✅ تم إعادة تحميل مواقيت الصلاة بنجاح');
            return times;
        } else {
            console.error('❌ فشل في الحصول على مواقيت الصلاة');
            return null;
        }
    } catch (error) {
        console.error('❌ خطأ في إعادة تحميل مواقيت الصلاة:', error);
        return getPrayerTimesFromDatabase(); // العودة للمواقيت المحفوظة
    }
}

// دالة لتحديث المستطيل الأفقي لمواقيت الصلاة
function updateHorizontalPrayerTimesDisplay(times) {
    try {
        if (!times) return;

        // تحديث المواقيت في المستطيل الأفقي
        const prayerElements = {
            'fajr': document.querySelector('.prayer-time[data-prayer="fajr"]'),
            'sunrise': document.querySelector('.prayer-time[data-prayer="sunrise"]'),
            'dhuhr': document.querySelector('.prayer-time[data-prayer="dhuhr"]'),
            'asr': document.querySelector('.prayer-time[data-prayer="asr"]'),
            'maghrib': document.querySelector('.prayer-time[data-prayer="maghrib"]'),
            'isha': document.querySelector('.prayer-time[data-prayer="isha"]')
        };

        Object.keys(prayerElements).forEach(prayer => {
            const element = prayerElements[prayer];
            if (element && times[prayer]) {
                element.textContent = times[prayer];
                console.log(`✅ تم تحديث ${prayer}: ${times[prayer]}`);
            }
        });

        console.log('✅ تم تحديث المستطيل الأفقي لمواقيت الصلاة');
    } catch (error) {
        console.error('❌ خطأ في تحديث المستطيل الأفقي:', error);
    }
}

// تصدير الدوال
window.getPrayerTimes = getPrayerTimes;
window.updatePrayerTimesDisplay = updatePrayerTimesDisplay;
window.reloadPrayerTimes = reloadPrayerTimes;
window.PRAYER_TIMES_DATA = PRAYER_TIMES_DATA;
