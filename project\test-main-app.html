<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التطبيق الرئيسي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: center;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #40E0D0;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-button {
            background: #40E0D0;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        
        .test-button:hover {
            background: #369fb8;
        }
        
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #e8f4f8;
            border-radius: 5px;
            text-align: right;
        }
        
        .error {
            background: #ffe6e6;
            color: #d32f2f;
        }
        
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار التطبيق الرئيسي - الساعات والمناطق الزمنية</h1>
        
        <div class="test-section">
            <h3>اختبار دوال الساعة</h3>
            <button class="test-button" onclick="testClockFunctions()">اختبار دوال الساعة</button>
            <div id="clock-test-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>اختبار قاعدة بيانات المدن</h3>
            <button class="test-button" onclick="testCitiesDatabase()">اختبار قاعدة البيانات</button>
            <div id="database-test-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>اختبار تحويل أسماء المدن</h3>
            <button class="test-button" onclick="testCityMapping()">اختبار التحويل</button>
            <div id="mapping-test-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>اختبار المناطق الزمنية</h3>
            <button class="test-button" onclick="testTimezones()">اختبار المناطق الزمنية</button>
            <div id="timezone-test-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>اختبار تحديث الساعات</h3>
            <select id="test-city-select" onchange="testCityChange()">
                <option value="Asia/Amman">عمان - الأردن</option>
                <option value="Asia/Riyadh">الرياض - السعودية</option>
                <option value="Asia/Dubai">دبي - الإمارات</option>
                <option value="Africa/Cairo">القاهرة - مصر</option>
            </select>
            <div id="city-change-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>معلومات الحالة الحالية</h3>
            <button class="test-button" onclick="showCurrentStatus()">عرض الحالة</button>
            <div id="status-result" class="result"></div>
        </div>
    </div>

    <!-- تحميل المكتبات المطلوبة -->
    <script src="cities-database.js"></script>
    <script src="clock-new.js"></script>
    
    <script>
        function testClockFunctions() {
            const result = document.getElementById('clock-test-result');
            let output = '<h4>نتائج اختبار دوال الساعة:</h4>';
            
            try {
                // اختبار دالة الوقت الحالي
                if (typeof getCurrentTimeForSelectedCity === 'function') {
                    const cityTime = getCurrentTimeForSelectedCity();
                    output += `✅ دالة getCurrentTimeForSelectedCity تعمل: ${cityTime.toLocaleTimeString()}<br>`;
                } else {
                    output += `❌ دالة getCurrentTimeForSelectedCity غير موجودة<br>`;
                }
                
                // اختبار دوال الساعة الأخرى
                const functions = ['updateAnalogClock', 'updateDigitalClock', 'updateDate', 'updateCountdown'];
                functions.forEach(func => {
                    if (typeof window[func] === 'function') {
                        output += `✅ دالة ${func} موجودة<br>`;
                    } else {
                        output += `❌ دالة ${func} غير موجودة<br>`;
                    }
                });
                
                result.className = 'result success';
            } catch (error) {
                output += `❌ خطأ: ${error.message}`;
                result.className = 'result error';
            }
            
            result.innerHTML = output;
        }
        
        function testCitiesDatabase() {
            const result = document.getElementById('database-test-result');
            let output = '<h4>نتائج اختبار قاعدة البيانات:</h4>';
            
            try {
                if (typeof CITIES_DATABASE !== 'undefined') {
                    const cityCount = Object.keys(CITIES_DATABASE).length;
                    output += `✅ قاعدة بيانات المدن محملة: ${cityCount} مدينة<br>`;
                    
                    // اختبار بعض المدن
                    const testCities = ['Asia/Amman', 'Asia/Riyadh', 'Asia/Dubai', 'Africa/Cairo'];
                    testCities.forEach(city => {
                        if (CITIES_DATABASE[city]) {
                            output += `✅ ${city}: ${CITIES_DATABASE[city].name} - ${CITIES_DATABASE[city].timezone}<br>`;
                        } else {
                            output += `❌ ${city}: غير موجود<br>`;
                        }
                    });
                    
                    result.className = 'result success';
                } else {
                    output += `❌ قاعدة بيانات المدن غير محملة`;
                    result.className = 'result error';
                }
            } catch (error) {
                output += `❌ خطأ: ${error.message}`;
                result.className = 'result error';
            }
            
            result.innerHTML = output;
        }
        
        function testCityMapping() {
            const result = document.getElementById('mapping-test-result');
            let output = '<h4>نتائج اختبار تحويل أسماء المدن:</h4>';
            
            try {
                // اختبار تحويل أسماء المدن العربية
                const testMappings = [
                    {country: 'الأردن', city: 'عمان', expected: 'Asia/Amman'},
                    {country: 'السعودية', city: 'الرياض', expected: 'Asia/Riyadh'},
                    {country: 'الإمارات', city: 'دبي', expected: 'Asia/Dubai'},
                    {country: 'مصر', city: 'القاهرة', expected: 'Africa/Cairo'}
                ];
                
                testMappings.forEach(test => {
                    // محاكاة دالة getCityTimezoneKey (نحتاج لنسخها هنا للاختبار)
                    const cityMapping = {
                        "الأردن": {"عمان": "Asia/Amman"},
                        "السعودية": {"الرياض": "Asia/Riyadh"},
                        "الإمارات": {"دبي": "Asia/Dubai"},
                        "مصر": {"القاهرة": "Africa/Cairo"}
                    };
                    
                    const result_key = cityMapping[test.country] && cityMapping[test.country][test.city];
                    if (result_key === test.expected) {
                        output += `✅ ${test.city} → ${result_key}<br>`;
                    } else {
                        output += `❌ ${test.city} → ${result_key} (متوقع: ${test.expected})<br>`;
                    }
                });
                
                result.className = 'result success';
            } catch (error) {
                output += `❌ خطأ: ${error.message}`;
                result.className = 'result error';
            }
            
            result.innerHTML = output;
        }
        
        function testTimezones() {
            const result = document.getElementById('timezone-test-result');
            let output = '<h4>نتائج اختبار المناطق الزمنية:</h4>';
            
            try {
                const testTimezones = ['Asia/Amman', 'Asia/Riyadh', 'Asia/Dubai', 'Africa/Cairo'];
                const now = new Date();
                
                testTimezones.forEach(timezone => {
                    try {
                        const cityTime = new Date(now.toLocaleString("en-US", {timeZone: timezone}));
                        output += `✅ ${timezone}: ${cityTime.toLocaleTimeString()}<br>`;
                    } catch (error) {
                        output += `❌ ${timezone}: خطأ في المنطقة الزمنية<br>`;
                    }
                });
                
                result.className = 'result success';
            } catch (error) {
                output += `❌ خطأ: ${error.message}`;
                result.className = 'result error';
            }
            
            result.innerHTML = output;
        }
        
        function testCityChange() {
            const result = document.getElementById('city-change-result');
            const selectedCity = document.getElementById('test-city-select').value;
            
            try {
                // محاكاة تغيير المدينة
                localStorage.setItem('selectedCity', selectedCity);
                
                if (typeof getCurrentTimeForSelectedCity === 'function') {
                    const cityTime = getCurrentTimeForSelectedCity();
                    result.innerHTML = `✅ تم تغيير المدينة إلى ${selectedCity}<br>الوقت الجديد: ${cityTime.toLocaleTimeString()}`;
                    result.className = 'result success';
                } else {
                    result.innerHTML = `❌ دالة getCurrentTimeForSelectedCity غير متوفرة`;
                    result.className = 'result error';
                }
            } catch (error) {
                result.innerHTML = `❌ خطأ: ${error.message}`;
                result.className = 'result error';
            }
        }
        
        function showCurrentStatus() {
            const result = document.getElementById('status-result');
            let output = '<h4>الحالة الحالية:</h4>';
            
            try {
                const selectedCity = localStorage.getItem('selectedCity') || 'غير محدد';
                const localTime = new Date().toLocaleTimeString();
                
                output += `المدينة المحفوظة: ${selectedCity}<br>`;
                output += `الوقت المحلي: ${localTime}<br>`;
                
                if (typeof getCurrentTimeForSelectedCity === 'function') {
                    const cityTime = getCurrentTimeForSelectedCity();
                    output += `وقت المدينة: ${cityTime.toLocaleTimeString()}<br>`;
                }
                
                result.className = 'result';
            } catch (error) {
                output += `❌ خطأ: ${error.message}`;
                result.className = 'result error';
            }
            
            result.innerHTML = output;
        }
        
        // تحديث الحالة كل ثانية
        setInterval(showCurrentStatus, 1000);
        
        // عرض الحالة الأولية
        setTimeout(showCurrentStatus, 500);
    </script>
</body>
</html>
